<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Analyse de Tendances{% endblock %}</title>

    <!-- Bootstrap 5.3 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Custom CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <!-- Enhanced UX CSS -->
    <link rel="stylesheet" href="{% static 'css/enhanced-ux.css' %}">
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3a0ca3;
            --accent-color: #4cc9f0;
            --success-color: #4ade80;
            --warning-color: #fbbf24;
            --danger-color: #f87171;
            --light-color: #f8fafc;
            --dark-color: #1e293b;
            --gray-color: #94a3b8;
        }

        body {
            font-family: "Times New Roman", Times, serif !important;
            background-color: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
        }

        /* Override all font families to Times New Roman */
        * {
            font-family: "Times New Roman", Times, serif !important;
        }

        /* Navbar Styling */
        .navbar {
            padding: 1rem 0;
            background-color: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
        }

        .nav-link {
            font-weight: 500;
            padding: 0.5rem 1rem;
            margin: 0 0.25rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary-color) !important;
            background-color: rgba(67, 97, 238, 0.1);
            transform: translateY(-2px);
        }

        .dropdown-menu {
            border: none;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            padding: 0.5rem;
        }

        .dropdown-item {
            border-radius: 8px;
            padding: 0.6rem 1rem;
            transition: all 0.2s ease;
        }

        .dropdown-item:hover {
            background-color: rgba(67, 97, 238, 0.1);
            color: var(--primary-color);
        }

        /* Premium Badge Styling */
        .premium-badge {
            animation: pulse 2s infinite;
            font-weight: 600;
            padding: 0.35rem 0.65rem;
            box-shadow: 0 0 10px rgba(74, 222, 128, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.5);
            font-size: 0.9rem;
            letter-spacing: 0.5px;
            text-transform: uppercase;
            background-color: #22c55e !important; /* Force green color */
            color: white !important;
            position: relative;
            z-index: 10;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(74, 222, 128, 0.9);
            }
            70% {
                box-shadow: 0 0 0 8px rgba(74, 222, 128, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(74, 222, 128, 0);
            }
        }

        /* Make sure premium badge is always visible */
        .nav-link .premium-badge {
            display: inline-flex !important;
            align-items: center;
            justify-content: center;
        }

        /* Premium menu item styling */
        .premium-menu-item {
            border: 1px solid rgba(74, 222, 128, 0.5);
            box-shadow: 0 4px 12px rgba(74, 222, 128, 0.2);
            transition: all 0.3s ease;
        }

        .premium-menu-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(74, 222, 128, 0.3);
        }

        /* Button Styling */
        .btn {
            border-radius: 8px;
            padding: 0.6rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(67, 97, 238, 0.2);
        }

        /* Card Styling */
        .card {
            border: none;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        /* Alert Styling */
        .alert {
            border: none;
            border-radius: 12px;
            padding: 1rem 1.5rem;
        }

        /* Footer Styling */
        footer {
            background-color: #f1f5f9;
            padding: 4rem 0 0;
            margin-top: 5rem;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }

        footer h5 {
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--dark-color);
        }

        footer a {
            color: var(--gray-color);
            text-decoration: none;
            transition: all 0.2s ease;
            display: inline-block;
            margin-bottom: 0.5rem;
        }

        footer a:hover {
            color: var(--primary-color);
            transform: translateX(5px);
        }

        .footer-bottom {
            background-color: #e2e8f0;
            padding: 1.5rem 0;
            margin-top: 3rem;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>

<!-- Admin Status Indicator -->
{% if user.is_authenticated and user.is_staff %}
<div class="admin-status-indicator" onclick="window.location.href='{% url 'admin_dashboard' %}'" title="Mode Administrateur - Cliquez pour accéder au dashboard admin">
    <i class="bi bi-shield-check"></i>
    Mode Admin
</div>
{% endif %}

<!-- Admin Footer Indicator -->
{% if user.is_authenticated and user.is_staff %}
<div class="admin-footer-indicator" title="Connecté en tant qu'administrateur">
    <i class="bi bi-person-gear me-1"></i>
    Admin: {{ user.username }}
</div>
{% endif %}

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
    <div class="container">
        <a class="navbar-brand" href="{% url 'home' %}">
            <i class="bi bi-graph-up-arrow me-2"></i>Analyse de Tendances
            {% if user.is_authenticated and user.is_staff %}
            <span class="admin-mode-badge ms-2">
                <i class="bi bi-shield-check me-1"></i>Admin
            </span>
            {% endif %}
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse justify-content-between" id="navbarNav">
            <ul class="navbar-nav">
                <li class="nav-item"><a class="nav-link" href="{% url 'home' %}"><i class="bi bi-house-door me-1"></i>Accueil</a></li>
                {% if user.is_authenticated %}
                    <li class="nav-item"><a class="nav-link" href="{% url 'dashboard' %}"><i class="bi bi-speedometer2 me-1"></i>Tableau de bord</a></li>
                    <li class="nav-item"><a class="nav-link" href="{% url 'search' %}"><i class="bi bi-search me-1"></i>Rechercher</a></li>
                {% endif %}
            </ul>
            <ul class="navbar-nav">
                {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                            <div class="{% if user.profile.is_premium %}bg-success{% else %}bg-primary{% endif %} rounded-circle text-white d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                <span>{{ user.username|slice:":1" }}</span>
                            </div>
                            <span>{{ user.username }}</span>
                            {% if user.profile.is_premium %}
                            <span class="badge bg-success ms-2 premium-badge" title="Compte Premium">
                                <i class="bi bi-star-fill"></i> Premium
                            </span>
                            {% endif %}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            {% if user.profile.is_premium %}
                            <li>
                                <div class="dropdown-item-text bg-success bg-opacity-10 p-3 mb-2 rounded premium-menu-item">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-star-fill text-success fs-3 me-3"></i>
                                        <div>
                                            <strong class="d-block fs-5 mb-1">Compte Premium</strong>
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-infinity text-success me-1"></i>
                                                <span class="fw-semibold text-success">Recherches illimitées</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            {% endif %}
                            <li><a class="dropdown-item" href="{% url 'profile' %}"><i class="bi bi-person me-2"></i>Profil</a></li>
                            <li><a class="dropdown-item" href="{% url 'subscription' %}">
                                {% if user.profile.is_premium %}
                                <i class="bi bi-star-fill me-2 text-warning"></i>Gérer l'abonnement
                                {% else %}
                                <i class="bi bi-star me-2"></i>Passer à Premium
                                {% endif %}
                            </a></li>
                            {% if user.is_staff %}
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger fw-bold" href="{% url 'admin_dashboard' %}">
                                    <i class="bi bi-shield-check me-2"></i>Administration
                                </a>
                            </li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="post" action="{% url 'logout' %}">
                                    {% csrf_token %}
                                    <button type="submit" class="dropdown-item"><i class="bi bi-box-arrow-right me-2"></i>Déconnexion</button>
                                </form>
                            </li>
                        </ul>
                    </li>
                {% else %}
                    <li class="nav-item"><a class="nav-link" href="{% url 'login' %}"><i class="bi bi-box-arrow-in-right me-1"></i>Connexion</a></li>
                    <li class="nav-item"><a class="btn btn-primary ms-2" href="{% url 'register' %}"><i class="bi bi-person-plus me-1"></i>Inscription</a></li>
                {% endif %}
            </ul>
        </div>
    </div>
</nav>

<!-- Main Content -->
<div class="container mt-4">
    {% if messages %}
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert" data-aos="fade-up" data-aos-delay="100">
            <i class="bi {% if message.tags == 'success' %}bi-check-circle{% elif message.tags == 'warning' %}bi-exclamation-triangle{% elif message.tags == 'error' %}bi-x-circle{% else %}bi-info-circle{% endif %} me-2"></i>
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endfor %}
    {% endif %}
    {% block content %}{% endblock %}
</div>

<!-- Footer -->
<footer>
    <div class="container">
        <div class="row">
            <div class="col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="100">
                <h5><i class="bi bi-graph-up-arrow me-2"></i>Analyse de Tendances</h5>
                <p class="text-muted">Application SaaS pour visualiser et analyser les tendances de recherche en temps réel.</p>
                <div class="d-flex mt-4">
                    <a href="#" class="me-3 text-primary"><i class="bi bi-twitter-x fs-5"></i></a>
                    <a href="#" class="me-3 text-primary"><i class="bi bi-facebook fs-5"></i></a>
                    <a href="#" class="me-3 text-primary"><i class="bi bi-linkedin fs-5"></i></a>
                    <a href="#" class="text-primary"><i class="bi bi-instagram fs-5"></i></a>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 mb-4" data-aos="fade-up" data-aos-delay="200">
                <h5>Navigation</h5>
                <ul class="list-unstyled">
                    <li><a href="{% url 'home' %}"><i class="bi bi-chevron-right me-1"></i>Accueil</a></li>
                    {% if user.is_authenticated %}
                    <li><a href="{% url 'dashboard' %}"><i class="bi bi-chevron-right me-1"></i>Tableau de bord</a></li>
                    <li><a href="{% url 'search' %}"><i class="bi bi-chevron-right me-1"></i>Rechercher</a></li>
                    {% else %}
                    <li><a href="{% url 'login' %}"><i class="bi bi-chevron-right me-1"></i>Connexion</a></li>
                    <li><a href="{% url 'register' %}"><i class="bi bi-chevron-right me-1"></i>Inscription</a></li>
                    {% endif %}
                </ul>
            </div>
            <div class="col-lg-2 col-md-4 mb-4" data-aos="fade-up" data-aos-delay="300">
                <h5>Ressources</h5>
                <ul class="list-unstyled">
                    <li><a href="#"><i class="bi bi-chevron-right me-1"></i>Documentation</a></li>
                    <li><a href="#"><i class="bi bi-chevron-right me-1"></i>Blog</a></li>
                    <li><a href="#"><i class="bi bi-chevron-right me-1"></i>FAQ</a></li>
                    <li><a href="#"><i class="bi bi-chevron-right me-1"></i>Support</a></li>
                </ul>
            </div>
            <div class="col-lg-4 col-md-4 mb-4" data-aos="fade-up" data-aos-delay="400">
                <h5>Contact</h5>
                <ul class="list-unstyled">
                    <li class="mb-2"><i class="bi bi-envelope me-2 text-primary"></i><EMAIL></li>
                    <li class="mb-2"><i class="bi bi-telephone me-2 text-primary"></i>+33 1 23 45 67 89</li>
                    <li><i class="bi bi-geo-alt me-2 text-primary"></i>Paris, France</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="footer-bottom text-center">
        <div class="container">
            <p class="mb-0">© 2025 Analyse de Tendances — Tous droits réservés</p>
        </div>
    </div>
</footer>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<!-- AOS Animation Library -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- Enhanced UX JavaScript -->
<script src="{% static 'js/enhanced-ux.js' %}"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });
    });
</script>

{% block extra_js %}{% endblock %}
</body>
</html>
