#!/usr/bin/env python
"""
Script de correction pour les problèmes d'authentification
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trend_clone.settings')
django.setup()

from django.contrib.auth.models import User
from core.models import UserProfile

def fix_authentication_issues():
    """Corrige les problèmes d'authentification"""
    print("=== CORRECTION DES PROBLÈMES D'AUTHENTIFICATION ===\n")
    
    # 1. S'assurer que tous les utilisateurs ont des profils
    print("1. VÉRIFICATION ET CRÉATION DES PROFILS MANQUANTS:")
    users_without_profile = []
    
    for user in User.objects.all():
        try:
            profile = user.profile
        except UserProfile.DoesNotExist:
            users_without_profile.append(user)
    
    if users_without_profile:
        print(f"   Utilisateurs sans profil trouvés: {len(users_without_profile)}")
        for user in users_without_profile:
            profile = UserProfile.objects.create(user=user)
            print(f"   - Profil créé pour {user.username}")
    else:
        print("   ✅ Tous les utilisateurs ont des profils")
    
    print("\n" + "="*50)
    
    # 2. Vérifier les utilisateurs inactifs
    print("\n2. VÉRIFICATION DES COMPTES INACTIFS:")
    inactive_users = User.objects.filter(is_active=False)
    if inactive_users.exists():
        print(f"   Utilisateurs inactifs trouvés: {inactive_users.count()}")
        for user in inactive_users:
            print(f"   - {user.username} (créé le {user.date_joined})")
            # Optionnel: réactiver les comptes
            # user.is_active = True
            # user.save()
            # print(f"     → Compte réactivé")
    else:
        print("   ✅ Tous les utilisateurs sont actifs")
    
    print("\n" + "="*50)
    
    # 3. Créer un utilisateur de test avec mot de passe connu
    print("\n3. CRÉATION D'UTILISATEUR DE TEST:")
    test_username = "test_user_login"
    test_email = "<EMAIL>"
    test_password = "testlogin123"
    
    # Supprimer l'utilisateur de test s'il existe
    User.objects.filter(username=test_username).delete()
    
    # Créer le nouvel utilisateur de test
    test_user = User.objects.create_user(
        username=test_username,
        email=test_email,
        password=test_password
    )
    
    print(f"   Utilisateur de test créé:")
    print(f"   - Nom d'utilisateur: {test_username}")
    print(f"   - Email: {test_email}")
    print(f"   - Mot de passe: {test_password}")
    print(f"   - Actif: {test_user.is_active}")
    
    # Vérifier le profil
    try:
        profile = test_user.profile
        print(f"   - Profil créé automatiquement: ✅")
    except UserProfile.DoesNotExist:
        profile = UserProfile.objects.create(user=test_user)
        print(f"   - Profil créé manuellement: ✅")
    
    print("\n" + "="*50)
    
    # 4. Réinitialiser les mots de passe de quelques utilisateurs existants
    print("\n4. RÉINITIALISATION DE MOTS DE PASSE POUR TESTS:")
    
    test_users = [
        ('soso11', 'password123'),
        ('soso1', 'password123'),
        ('CHB', 'password123'),
    ]
    
    for username, password in test_users:
        user = User.objects.filter(username=username).first()
        if user:
            user.set_password(password)
            user.save()
            print(f"   - {username}: mot de passe réinitialisé → {password}")
        else:
            print(f"   - {username}: utilisateur non trouvé")
    
    print("\n" + "="*50)
    
    # 5. Résumé des comptes de test disponibles
    print("\n5. COMPTES DE TEST DISPONIBLES:")
    print("   Vous pouvez maintenant tester la connexion avec:")
    print(f"   - {test_username} / {test_password} (nouveau compte)")
    
    for username, password in test_users:
        user = User.objects.filter(username=username).first()
        if user:
            print(f"   - {username} / {password} (compte existant)")
    
    print(f"\n   URL de connexion: http://127.0.0.1:8000/login/")
    
    print("\n" + "="*50)
    print("CORRECTION TERMINÉE")

if __name__ == "__main__":
    fix_authentication_issues()
