{% extends 'admin/base.html' %}

{% block title %}<PERSON>étail Utilisateur - Administration{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2 mb-0">Détail de l'utilisateur</h1>
        <a href="{% url 'admin_dashboard' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>Retour au tableau de bord
        </a>
    </div>

    {% if messages %}
    <div class="row">
        <div class="col-12">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <div class="row">
        <!-- Informations utilisateur -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Informations utilisateur</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-muted mb-1">Nom d'utilisateur</h6>
                        <p class="mb-0 fs-5">{{ user_profile.user.username }}</p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-muted mb-1">Email</h6>
                        <p class="mb-0">{{ user_profile.user.email }}</p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-muted mb-1">Date d'inscription</h6>
                        <p class="mb-0">{{ user_profile.user.date_joined|date:"d F Y à H:i" }}</p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-muted mb-1">Statut</h6>
                        {% if user_profile.is_premium %}
                        <span class="badge bg-success fs-6 px-3 py-2">Premium</span>
                        {% else %}
                        <span class="badge bg-secondary fs-6 px-3 py-2">Gratuit</span>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <h6 class="text-muted mb-1">Recherches effectuées</h6>
                        <p class="mb-0">{{ searches.count }}</p>
                    </div>
                    {% if user_profile.is_premium and user_profile.subscription_end_date %}
                    <div class="mb-3">
                        <h6 class="text-muted mb-1">Fin d'abonnement</h6>
                        <p class="mb-0">{{ user_profile.subscription_end_date|date:"d F Y" }}</p>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer bg-light">
                    <div class="d-grid gap-2">
                        {% if user_profile.is_premium %}
                        <form method="post">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="remove_premium">
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="bi bi-x-circle me-2"></i>Retirer le statut Premium
                            </button>
                        </form>
                        {% else %}
                        <form method="post">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="make_premium">
                            <button type="submit" class="btn btn-success w-100">
                                <i class="bi bi-star-fill me-2"></i>Passer en Premium
                            </button>
                        </form>
                        {% endif %}
                        <a href="{{ stripe_dashboard_url }}" target="_blank" class="btn btn-outline-primary">
                            <i class="bi bi-stripe me-2"></i>Tableau de bord Stripe
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Historique des recherches -->
        <div class="col-lg-8 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Historique des recherches</h5>
                </div>
                <div class="card-body p-0">
                    {% if searches %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Requête</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for search in searches %}
                                <tr>
                                    <td>{{ search.query }}</td>
                                    <td>{{ search.timestamp|date:"d/m/Y H:i" }}</td>
                                    <td>
                                        <a href="{% url 'result' search.id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-search fs-1 text-muted"></i>
                        <p class="mt-3 text-muted">Aucune recherche effectuée</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Historique des abonnements -->
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Historique des abonnements</h5>
                </div>
                <div class="card-body p-0">
                    {% if subscriptions %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>ID Stripe</th>
                                    <th>Statut</th>
                                    <th>Date de début</th>
                                    <th>Date de fin</th>
                                    <th>Créé le</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for subscription in subscriptions %}
                                <tr>
                                    <td>{{ subscription.stripe_subscription_id }}</td>
                                    <td>
                                        {% if subscription.status == 'active' %}
                                        <span class="badge bg-success">Actif</span>
                                        {% elif subscription.status == 'canceled' %}
                                        <span class="badge bg-danger">Annulé</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ subscription.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ subscription.start_date|date:"d/m/Y" }}</td>
                                    <td>{{ subscription.end_date|date:"d/m/Y" }}</td>
                                    <td>{{ subscription.created_at|date:"d/m/Y H:i" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-credit-card fs-1 text-muted"></i>
                        <p class="mt-3 text-muted">Aucun abonnement trouvé</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
