{% extends 'base.html' %}
{% load core_extras %}

{% block title %}Tableau de bord - Analyse de Tendances{% endblock %}

{% block extra_css %}
<style>
    .dashboard-header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border-radius: 20px;
        padding: 3rem 2rem;
        margin-bottom: 3rem;
        position: relative;
        overflow: hidden;
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="2"/></svg>');
        background-size: 100px 100px;
        opacity: 0.3;
    }

    .dashboard-header h1 {
        position: relative;
        z-index: 2;
        margin: 0;
        font-weight: 700;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .dashboard-header p {
        position: relative;
        z-index: 2;
        margin: 0;
        opacity: 0.9;
        font-size: 1.1rem;
    }

    .stats-card {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.08);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border: none;
        position: relative;
        overflow: hidden;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        transform: scaleX(0);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .stats-card:hover::before {
        transform: scaleX(1);
    }

    .stats-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        margin-bottom: 1rem;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .stats-card:hover .stats-icon {
        transform: scale(1.1) rotate(5deg);
    }

    .premium-icon {
        background: linear-gradient(135deg, #22c55e, #16a34a);
        color: white;
    }

    .search-icon {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
    }

    .action-card {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.08);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border: none;
        height: 100%;
    }

    .action-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.12);
    }

    .action-btn {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border: none;
        border-radius: 12px;
        padding: 1rem 2rem;
        color: white;
        font-weight: 600;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        width: 100%;
        margin-bottom: 1rem;
    }

    .action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .action-btn:hover::before {
        left: 100%;
    }

    .action-btn:hover {
        transform: translateY(-3px) scale(1.02);
        box-shadow: 0 15px 30px rgba(67, 97, 238, 0.4);
        color: white;
    }

    .recent-searches-card {
        background: white;
        border-radius: 16px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.08);
        border: none;
        overflow: hidden;
    }

    .table-modern {
        margin: 0;
    }

    .table-modern thead th {
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        border: none;
        font-weight: 600;
        color: var(--dark-color);
        padding: 1.2rem 1.5rem;
    }

    .table-modern tbody tr {
        transition: all 0.3s ease;
        border: none;
    }

    .table-modern tbody tr:hover {
        background: rgba(67, 97, 238, 0.05);
        transform: scale(1.01);
    }

    .table-modern tbody td {
        padding: 1.2rem 1.5rem;
        border: none;
        vertical-align: middle;
    }

    .search-query {
        font-weight: 600;
        color: var(--primary-color);
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: var(--gray-color);
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1.5rem;
        opacity: 0.5;
    }

    .progress-ring {
        width: 80px;
        height: 80px;
        margin: 0 auto 1rem;
    }

    .progress-ring circle {
        fill: none;
        stroke-width: 8;
        stroke-linecap: round;
        transform: rotate(-90deg);
        transform-origin: 50% 50%;
    }

    .progress-ring .bg {
        stroke: #e2e8f0;
    }

    .progress-ring .progress {
        stroke: var(--primary-color);
        stroke-dasharray: 251.2;
        stroke-dashoffset: 251.2;
        animation: progress 2s ease-out forwards;
    }

    @keyframes progress {
        to {
            stroke-dashoffset: calc(251.2 - (251.2 * var(--progress)) / 100);
        }
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in {
        animation: fadeInUp 0.8s ease-out;
    }

    .animate-delay-1 { animation-delay: 0.1s; animation-fill-mode: both; }
    .animate-delay-2 { animation-delay: 0.2s; animation-fill-mode: both; }
    .animate-delay-3 { animation-delay: 0.3s; animation-fill-mode: both; }
    .animate-delay-4 { animation-delay: 0.4s; animation-fill-mode: both; }
</style>
{% endblock %}

{% block content %}
<!-- Dashboard Header -->
<div class="dashboard-header animate-fade-in" data-aos="fade-up">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1>Bonjour, {{ user.first_name|default:user.username }} ! 👋</h1>
            <p class="mb-0">Bienvenue sur votre tableau de bord. Explorez les tendances et découvrez des insights précieux.</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="d-inline-flex align-items-center">
                <i class="bi bi-calendar3 me-2"></i>
                <span>{{ "now"|date:"d F Y" }}</span>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-4 animate-fade-in animate-delay-1" data-aos="fade-up" data-aos-delay="100">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon {% if user_profile.is_premium %}premium-icon{% else %}search-icon{% endif %}">
                    {% if user_profile.is_premium %}
                    <i class="bi bi-star-fill"></i>
                    {% else %}
                    <i class="bi bi-person-circle"></i>
                    {% endif %}
                </div>
                <div class="flex-grow-1">
                    <h6 class="text-muted mb-1">Statut du compte</h6>
                    <h4 class="mb-0">
                        {% if user_profile.is_premium %}
                        <span class="text-success">Premium</span>
                        {% else %}
                        <span class="text-secondary">Gratuit</span>
                        {% endif %}
                    </h4>
                </div>
            </div>
            {% if user_profile.is_premium and user_profile.subscription_end_date %}
            <div class="mt-3 pt-3 border-top">
                <small class="text-muted">
                    <i class="bi bi-calendar-check me-1"></i>
                    Actif jusqu'au {{ user_profile.subscription_end_date|date:"d/m/Y" }}
                </small>
            </div>
            {% endif %}
        </div>
    </div>

    <div class="col-md-4 animate-fade-in animate-delay-2" data-aos="fade-up" data-aos-delay="200">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon search-icon">
                    <i class="bi bi-search"></i>
                </div>
                <div class="flex-grow-1">
                    <h6 class="text-muted mb-1">Recherches</h6>
                    <h4 class="mb-0">
                        {% if user_profile.is_premium %}
                        <span class="text-success">Illimitées</span>
                        {% else %}
                        <span class="{% if remaining_searches == 0 %}text-danger{% elif remaining_searches == 1 %}text-warning{% else %}text-success{% endif %}">
                            {{ remaining_searches }}/3
                        </span>
                        {% endif %}
                    </h4>
                </div>
            </div>
            {% if not user_profile.is_premium %}
            <div class="mt-3">
                <div class="progress" style="height: 8px; border-radius: 4px;">
                    <div class="progress-bar bg-{% if remaining_searches == 0 %}danger{% elif remaining_searches == 1 %}warning{% else %}success{% endif %}"
                         style="width: {% widthratio remaining_searches 3 100 %}%; transition: width 1s ease-out;"></div>
                </div>
                <small class="text-muted mt-2 d-block">{{ user_profile.search_count }} utilisées sur 3</small>
            </div>
            {% endif %}
        </div>
    </div>

    <div class="col-md-4 animate-fade-in animate-delay-3" data-aos="fade-up" data-aos-delay="300">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, #f093fb, #f5576c); color: white;">
                    <i class="bi bi-clock-history"></i>
                </div>
                <div class="flex-grow-1">
                    <h6 class="text-muted mb-1">Recherches récentes</h6>
                    <h4 class="mb-0">{{ searches|length }}</h4>
                </div>
            </div>
            <div class="mt-3 pt-3 border-top">
                <small class="text-muted">
                    <i class="bi bi-activity me-1"></i>
                    Dernière activité aujourd'hui
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Action Cards -->
<div class="row mb-4">
    <div class="col-md-6 animate-fade-in animate-delay-4" data-aos="fade-up" data-aos-delay="400">
        <div class="action-card">
            <div class="text-center">
                <div class="stats-icon search-icon mx-auto">
                    <i class="bi bi-search"></i>
                </div>
                <h5 class="mb-3">Nouvelle Recherche</h5>
                <p class="text-muted mb-4">Explorez les tendances de recherche et découvrez des insights précieux sur les sujets populaires.</p>
                <a href="{% url 'search' %}" class="action-btn">
                    <i class="bi bi-search me-2"></i>Commencer une recherche
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-6 animate-fade-in animate-delay-4" data-aos="fade-up" data-aos-delay="500">
        <div class="action-card">
            <div class="text-center">
                {% if user_profile.is_premium %}
                <div class="stats-icon premium-icon mx-auto">
                    <i class="bi bi-star-fill"></i>
                </div>
                <h5 class="mb-3">Gérer l'abonnement</h5>
                <p class="text-muted mb-4">Consultez les détails de votre abonnement premium et gérez vos préférences de facturation.</p>
                <a href="{% url 'subscription' %}" class="action-btn" style="background: linear-gradient(135deg, #22c55e, #16a34a);">
                    <i class="bi bi-gear me-2"></i>Gérer Premium
                </a>
                {% else %}
                <div class="stats-icon mx-auto" style="background: linear-gradient(135deg, #fbbf24, #f59e0b); color: white;">
                    <i class="bi bi-star"></i>
                </div>
                <h5 class="mb-3">Passer à Premium</h5>
                <p class="text-muted mb-4">Débloquez des recherches illimitées et accédez à des fonctionnalités avancées d'analyse.</p>
                <a href="{% url 'subscription' %}" class="action-btn" style="background: linear-gradient(135deg, #fbbf24, #f59e0b);">
                    <i class="bi bi-arrow-up-circle me-2"></i>Découvrir Premium
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Searches -->
<div class="row">
    <div class="col-md-12">
        <div class="recent-searches-card animate-fade-in animate-delay-4" data-aos="fade-up" data-aos-delay="600">
            <div class="card-header" style="background: linear-gradient(135deg, #f8fafc, #e2e8f0); border: none; padding: 2rem 2rem 1rem;">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1">Recherches Récentes</h5>
                        <p class="text-muted mb-0">Consultez l'historique de vos dernières recherches</p>
                    </div>
                    <div class="stats-icon" style="background: linear-gradient(135deg, #f093fb, #f5576c); color: white; width: 50px; height: 50px; font-size: 1.2rem;">
                        <i class="bi bi-clock-history"></i>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                {% if searches %}
                <div class="table-responsive">
                    <table class="table table-modern">
                        <thead>
                            <tr>
                                <th><i class="bi bi-search me-2"></i>Requête</th>
                                <th><i class="bi bi-calendar3 me-2"></i>Date</th>
                                <th><i class="bi bi-graph-up me-2"></i>Résultats</th>
                                <th><i class="bi bi-gear me-2"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for search in searches %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="rounded-circle bg-primary bg-opacity-10 d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                            <i class="bi bi-search text-primary"></i>
                                        </div>
                                        <div>
                                            <span class="search-query">{{ search.query }}</span>
                                            <br>
                                            <small class="text-muted">Recherche de tendances</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <span class="fw-medium">{{ search.timestamp|date:"d M Y" }}</span>
                                        <br>
                                        <small class="text-muted">{{ search.timestamp|date:"H:i" }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-success bg-opacity-10 text-success px-3 py-2">
                                        <i class="bi bi-check-circle me-1"></i>Disponibles
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex gap-2">
                                        <a href="{% url 'result' search.id %}" class="btn btn-sm btn-primary" style="border-radius: 8px;">
                                            <i class="bi bi-eye me-1"></i>Voir
                                        </a>
                                        <button class="btn btn-sm btn-outline-secondary" style="border-radius: 8px;" onclick="shareSearch('{{ search.query }}')">
                                            <i class="bi bi-share"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="empty-state">
                    <i class="bi bi-search"></i>
                    <h5 class="mb-3">Aucune recherche effectuée</h5>
                    <p class="mb-4">Commencez à explorer les tendances en effectuant votre première recherche.</p>
                    <a href="{% url 'search' %}" class="action-btn" style="width: auto; display: inline-block;">
                        <i class="bi bi-search me-2"></i>Commencer à rechercher
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Animation pour les cartes de statistiques
    document.addEventListener('DOMContentLoaded', function() {
        // Animation des barres de progression
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 500);
        });

        // Animation des nombres
        const numbers = document.querySelectorAll('.stats-card h4');
        numbers.forEach(number => {
            const finalValue = number.textContent;
            if (!isNaN(finalValue)) {
                let currentValue = 0;
                const increment = finalValue / 50;
                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        currentValue = finalValue;
                        clearInterval(timer);
                    }
                    number.textContent = Math.floor(currentValue);
                }, 30);
            }
        });
    });

    // Fonction pour partager une recherche
    function shareSearch(query) {
        if (navigator.share) {
            navigator.share({
                title: 'Recherche de tendances',
                text: `Découvrez les tendances pour "${query}"`,
                url: window.location.href
            });
        } else {
            // Fallback pour les navigateurs qui ne supportent pas l'API Web Share
            const url = window.location.href;
            navigator.clipboard.writeText(`Découvrez les tendances pour "${query}": ${url}`);

            // Afficher une notification
            const toast = document.createElement('div');
            toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
            toast.style.zIndex = '9999';
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="bi bi-check-circle me-2"></i>Lien copié dans le presse-papiers !
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            document.body.appendChild(toast);

            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            setTimeout(() => {
                document.body.removeChild(toast);
            }, 3000);
        }
    }

    // Effet de hover pour les cartes
    document.querySelectorAll('.stats-card, .action-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
</script>
{% endblock %}
