"""
Views pour l'application Google Trends Clone
"""
# Imports Django
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate, logout
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
from django.utils import timezone

# Imports des modèles et formulaires
from .models import UserProfile, SearchResult, Subscription, SubscriptionEvent, Search
from .forms import CustomUserCreationForm, CustomAuthenticationForm, SearchForm, UserProfileForm
import json
import stripe
import datetime
import requests
import random
from datetime import timedelta

# Configuration de Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY

# Vues publiques
def home(request):
    """Page d'accueil avec choix entre utilisateur et administrateur"""
    return render(request, 'home.html')

def user_choice(request):
    """Page de choix de plan (gratuit ou premium)"""
    return render(request, 'user_choice.html')

# Vues d'authentification
def register(request):
    """Vue d'inscription utilisateur"""
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            # Spécifier le backend d'authentification
            login(request, user, backend='django.contrib.auth.backends.ModelBackend')
            messages.success(request, "Inscription réussie ! Bienvenue sur Clone Google Trends.")

            # Redirection en fonction du plan choisi
            plan = request.GET.get('plan')
            if plan == 'premium':
                return redirect('subscription')
            return redirect('dashboard')
    else:
        form = CustomUserCreationForm()

    return render(request, 'registration/register.html', {
        'form': form,
        'plan': request.GET.get('plan', 'free')
    })

def user_login(request):
    """Vue de connexion utilisateur"""
    if request.method == 'POST':
        form = CustomAuthenticationForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=username, password=password)
            if user is not None:
                login(request, user)
                messages.success(request, f"Bienvenue, {username} !")
                return redirect('dashboard')
    else:
        form = CustomAuthenticationForm()

    return render(request, 'registration/login.html', {'form': form})

# Les fonctions admin_register et admin_login ont été déplacées vers admin_views.py
# pour éviter les conflits et la duplication de code

@login_required
def logout_user(request):
    """Vue de déconnexion"""
    logout(request)
    messages.success(request, "Vous avez été déconnecté avec succès.")
    return redirect('home')

# Vues utilisateur
@login_required
def dashboard(request):
    """Tableau de bord utilisateur"""
    # Vérifier si l'utilisateur est un administrateur
    if request.user.is_staff:
        messages.info(request, "Vous êtes connecté en tant qu'administrateur. Utilisez l'interface d'administration.")
        return redirect('admin_dashboard')

    user_profile = request.user.profile
    searches = Search.objects.filter(user=request.user).order_by('-timestamp')[:10]

    # Vérifier le statut de l'abonnement
    if user_profile.is_premium and user_profile.subscription_end_date:
        if user_profile.subscription_end_date <= timezone.now():
            user_profile.is_premium = False
            user_profile.subscription_active = False
            user_profile.save()
            messages.warning(request, "Votre abonnement premium a expiré. Renouvelez-le pour continuer à bénéficier des avantages premium.")

    context = {
        'user_profile': user_profile,
        'searches': searches,
        'remaining_searches': user_profile.remaining_searches(),
        'can_search': user_profile.can_search()
    }

    return render(request, 'dashboard.html', context)

@login_required
def profile(request):
    """Profil utilisateur"""
    if request.method == 'POST':
        form = UserProfileForm(request.POST, instance=request.user.profile)
        if form.is_valid():
            form.save()
            messages.success(request, "Votre profil a été mis à jour avec succès.")
            return redirect('profile')
    else:
        form = UserProfileForm(instance=request.user.profile)

    context = {
        'form': form,
        'user_profile': request.user.profile
    }

    return render(request, 'profile.html', context)

@login_required
def search(request):
    """Vue de recherche de tendances"""
    user_profile = request.user.profile

    if request.method == 'POST':
        form = SearchForm(request.POST)
        if form.is_valid():
            query = form.cleaned_data['query']

            # Vérifier si l'utilisateur peut effectuer une recherche
            if not user_profile.can_search():
                messages.error(request, "Vous avez atteint votre limite de recherches gratuites. Passez à Premium pour continuer.")
                return redirect('subscription')

            # Créer la recherche
            search = Search.objects.create(
                user=request.user,
                query=query
            )

            # Incrémenter le compteur de recherches pour les utilisateurs gratuits
            if not user_profile.is_premium:
                user_profile.search_count += 1
                user_profile.save()

            try:
                # Récupérer les données de tendance avec Serper API
                trend_data = get_trend_data(query)

                print(f"Données de tendance récupérées pour '{query}':")
                for data_type, data in trend_data.items():
                    print(f"- {data_type}: {type(data)}")
                    if isinstance(data, dict):
                        print(f"  Clés: {list(data.keys())[:5]}...")
                    elif isinstance(data, list):
                        print(f"  Nombre d'éléments: {len(data)}")

                # Sauvegarder les résultats
                for data_type, data in trend_data.items():
                    # S'assurer que les données sont correctement sérialisées
                    if isinstance(data, dict) or isinstance(data, list):
                        data_json = json.dumps(data)
                    else:
                        data_json = str(data)

                    # Vérifier que les données sont bien sérialisées
                    print(f"Sauvegarde de {data_type}: {type(data_json)}, longueur: {len(data_json)}")

                    SearchResult.objects.create(
                        search=search,
                        data_type=data_type,
                        data=data_json
                    )

                return redirect('result', search_id=search.id)
            except Exception as e:
                messages.error(request, f"Erreur lors de la récupération des tendances: {str(e)}")
                # Supprimer la recherche en cas d'erreur
                search.delete()
                if not user_profile.is_premium:
                    user_profile.search_count -= 1
                    user_profile.save()
    else:
        form = SearchForm()

    context = {
        'form': form,
        'user_profile': user_profile
    }

    return render(request, 'search.html', context)

@login_required
def result(request, search_id):
    """Vue des résultats de recherche"""
    search = get_object_or_404(Search, id=search_id, user=request.user)

    # Récupérer les paramètres de filtre
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    country = request.GET.get('country')

    # Liste des pays disponibles pour le filtre
    countries = [
        {'code': 'FR', 'name': 'France'},
        {'code': 'US', 'name': 'États-Unis'},
        {'code': 'GB', 'name': 'Royaume-Uni'},
        {'code': 'DE', 'name': 'Allemagne'},
        {'code': 'ES', 'name': 'Espagne'}
    ]

    # Si des filtres sont appliqués, générer de nouvelles données
    if start_date or end_date or country:
        # Récupérer les données filtrées
        filtered_data = get_trend_data(search.query, start_date, end_date, country)

        # Utiliser directement les données filtrées
        chart_data = filtered_data
    else:
        # Utiliser les données existantes
        results = SearchResult.objects.filter(search=search)

        # Préparer les données pour les graphiques
        chart_data = {}
        for result in results:
            # Essayer de désérialiser les données JSON
            try:
                if isinstance(result.data, str) and (result.data.startswith('{') or result.data.startswith('[')):
                    chart_data[result.data_type] = json.loads(result.data)
                else:
                    chart_data[result.data_type] = result.data
            except json.JSONDecodeError:
                print(f"Erreur de décodage JSON pour {result.data_type}")
                chart_data[result.data_type] = result.data

    print(f"Données pour les graphiques pour la recherche '{search.query}':")
    for data_type, data in chart_data.items():
        print(f"- {data_type}: {type(data)}")
        if isinstance(data, dict):
            print(f"  Clés: {list(data.keys())[:5]}...")
        elif isinstance(data, list):
            print(f"  Nombre d'éléments: {len(data)}")
        elif isinstance(data, str):
            print(f"  Début de la chaîne: {data[:50]}...")

    # Créer des visualisations à partir des données
    visualizations = []

    # Vérifier si nous avons des données pour créer des visualisations
    if chart_data and any(chart_data.values()):
        # Créer une visualisation pour l'intérêt au fil du temps
        if 'interest_over_time' in chart_data and chart_data['interest_over_time']:
            # Essayer de convertir les données en dictionnaire si elles sont au format JSON
            interest_over_time = chart_data['interest_over_time']
            if isinstance(interest_over_time, str):
                try:
                    interest_over_time = json.loads(interest_over_time)
                except json.JSONDecodeError:
                    print(f"Erreur de décodage JSON pour interest_over_time: {interest_over_time[:100]}...")
                    interest_over_time = {}

            if interest_over_time:  # Vérifier que les données ne sont pas vides
                visualizations.append({
                    'visualization_type': {
                        'name': 'Graphique Linéaire',
                        'description': 'Évolution de l\'intérêt au fil du temps pour le terme recherché.'
                    },
                    'data': interest_over_time
                })

        # Créer une visualisation pour l'intérêt par région
        if 'interest_by_region' in chart_data and chart_data['interest_by_region']:
            # Essayer de convertir les données en dictionnaire si elles sont au format JSON
            interest_by_region = chart_data['interest_by_region']
            if isinstance(interest_by_region, str):
                try:
                    interest_by_region = json.loads(interest_by_region)
                except json.JSONDecodeError:
                    print(f"Erreur de décodage JSON pour interest_by_region: {interest_by_region[:100]}...")
                    interest_by_region = {}

            if interest_by_region:  # Vérifier que les données ne sont pas vides
                visualizations.append({
                    'visualization_type': {
                        'name': 'Carte Géographique',
                        'description': 'Répartition géographique de l\'intérêt pour le terme recherché.'
                    },
                    'data': interest_by_region
                })

        # Créer une visualisation pour les requêtes associées
        if 'related_queries' in chart_data and chart_data['related_queries']:
            # Essayer de convertir les données en dictionnaire si elles sont au format JSON
            related_queries = chart_data['related_queries']
            if isinstance(related_queries, str):
                try:
                    related_queries = json.loads(related_queries)
                except json.JSONDecodeError:
                    print(f"Erreur de décodage JSON pour related_queries: {related_queries[:100]}...")
                    related_queries = {}

            # Vérifier le format des données et extraire les requêtes populaires
            top_queries = None
            if isinstance(related_queries, dict):
                if 'top' in related_queries:
                    top_queries = related_queries['top']
                elif len(related_queries) > 0 and isinstance(list(related_queries.values())[0], dict) and 'top' in list(related_queries.values())[0]:
                    # Format de demo_data.py
                    query_key = list(related_queries.keys())[0]
                    top_queries = related_queries[query_key]['top']

            if top_queries:  # Vérifier que les données ne sont pas vides
                visualizations.append({
                    'visualization_type': {
                        'name': 'Graphique à Barres',
                        'description': 'Requêtes les plus populaires associées au terme recherché.'
                    },
                    'data': top_queries
                })

        # Créer une visualisation pour les sujets associés
        if 'related_topics' in chart_data and chart_data['related_topics']:
            # Essayer de convertir les données en dictionnaire si elles sont au format JSON
            related_topics = chart_data['related_topics']
            if isinstance(related_topics, str):
                try:
                    related_topics = json.loads(related_topics)
                except json.JSONDecodeError:
                    print(f"Erreur de décodage JSON pour related_topics: {related_topics[:100]}...")
                    related_topics = {}

            # Vérifier le format des données et extraire les sujets populaires
            top_topics = None
            if isinstance(related_topics, dict):
                if 'top' in related_topics:
                    top_topics = related_topics['top']
                elif len(related_topics) > 0 and isinstance(list(related_topics.values())[0], dict) and 'top' in list(related_topics.values())[0]:
                    # Format de demo_data.py
                    query_key = list(related_topics.keys())[0]
                    top_topics = related_topics[query_key]['top']

            if top_topics:  # Vérifier que les données ne sont pas vides
                visualizations.append({
                    'visualization_type': {
                        'name': 'Graphique Circulaire',
                        'description': 'Sujets les plus populaires associés au terme recherché.'
                    },
                    'data': top_topics
                })

    # Vérifier si des visualisations ont été créées
    if not visualizations:
        print(f"Aucune visualisation créée pour la recherche '{search.query}'")
        # Essayer de générer des données de démonstration comme dernier recours
        from core.demo_data import generate_demo_data
        demo_data = generate_demo_data(search.query, start_date, end_date, country)

        # Créer des visualisations à partir des données de démonstration
        if demo_data and 'interest_over_time' in demo_data and demo_data['interest_over_time']:
            visualizations.append({
                'visualization_type': {
                    'name': 'Graphique Linéaire',
                    'description': 'Évolution de l\'intérêt au fil du temps pour le terme recherché.'
                },
                'data': demo_data['interest_over_time']
            })

        if demo_data and 'interest_by_region' in demo_data and demo_data['interest_by_region']:
            visualizations.append({
                'visualization_type': {
                    'name': 'Carte Géographique',
                    'description': 'Répartition géographique de l\'intérêt pour le terme recherché.'
                },
                'data': demo_data['interest_by_region']
            })

        if demo_data and 'related_queries' in demo_data and demo_data['related_queries']:
            query_key = list(demo_data['related_queries'].keys())[0]
            top_queries = demo_data['related_queries'][query_key]['top']
            visualizations.append({
                'visualization_type': {
                    'name': 'Graphique à Barres',
                    'description': 'Requêtes les plus populaires associées au terme recherché.'
                },
                'data': top_queries
            })

        if demo_data and 'related_topics' in demo_data and demo_data['related_topics']:
            query_key = list(demo_data['related_topics'].keys())[0]
            top_topics = demo_data['related_topics'][query_key]['top']
            visualizations.append({
                'visualization_type': {
                    'name': 'Graphique Circulaire',
                    'description': 'Sujets les plus populaires associés au terme recherché.'
                },
                'data': top_topics
            })

        # Mettre à jour chart_data avec les données de démonstration
        chart_data = demo_data

        # Si toujours aucune visualisation, afficher un message d'erreur
        if not visualizations:
            messages.error(request, "Nous n'avons pas pu générer de visualisations pour cette recherche. Veuillez essayer avec un autre terme.")

    context = {
        'search_query': search,
        'chart_data': json.dumps(chart_data),
        'visualizations': visualizations,
        'countries': countries,
        'current_filters': {
            'start_date': start_date,
            'end_date': end_date,
            'country': country
        }
    }

    return render(request, 'result.html', context)

@login_required
def subscription(request):
    """Vue d'abonnement premium"""
    user_profile = request.user.profile

    context = {
        'user_profile': user_profile,
        'stripe_public_key': settings.STRIPE_PUBLISHABLE_KEY
    }

    return render(request, 'subscription.html', context)

@login_required
def create_checkout_session(request):
    """Crée une session de paiement Stripe"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)

    try:
        # Configurer Stripe avec la clé secrète
        stripe.api_key = settings.STRIPE_SECRET_KEY

        # Récupérer ou créer un client Stripe pour l'utilisateur
        user_profile = request.user.profile

        # Vérifier si l'utilisateur a déjà un ID client Stripe
        if user_profile.stripe_customer_id:
            stripe_customer_id = user_profile.stripe_customer_id
            print(f"Utilisation du client Stripe existant: {stripe_customer_id}")

            # Mettre à jour les informations du client si nécessaire
            stripe.Customer.modify(
                stripe_customer_id,
                email=request.user.email,
                name=request.user.get_full_name() or request.user.username,
                metadata={
                    'user_id': request.user.id
                }
            )
        else:
            # Vérifier si l'utilisateur a déjà un ID client Stripe dans ses abonnements
            subscriptions = Subscription.objects.filter(user=request.user)
            if subscriptions.exists() and subscriptions.first().stripe_customer_id:
                stripe_customer_id = subscriptions.first().stripe_customer_id
                print(f"Utilisation du client Stripe depuis l'abonnement: {stripe_customer_id}")

                # Enregistrer l'ID client Stripe dans le profil utilisateur
                user_profile.stripe_customer_id = stripe_customer_id
                user_profile.save()
            else:
                # Créer un nouveau client Stripe
                customer = stripe.Customer.create(
                    email=request.user.email,
                    name=request.user.get_full_name() or request.user.username,
                    metadata={
                        'user_id': request.user.id
                    }
                )
                stripe_customer_id = customer.id
                print(f"Nouveau client Stripe créé: {stripe_customer_id}")

                # Enregistrer l'ID client Stripe dans le profil utilisateur
                user_profile.stripe_customer_id = stripe_customer_id
                user_profile.save()
                print(f"ID client Stripe enregistré pour l'utilisateur {request.user.username}")

        # Créer la session de paiement
        checkout_session = stripe.checkout.Session.create(
            customer=stripe_customer_id,
            payment_method_types=['card'],
            line_items=[
                {
                    'price': settings.STRIPE_PRICE_ID,
                    'quantity': 1,
                },
            ],
            mode='subscription',
            success_url=request.build_absolute_uri('/subscription-success/'),
            cancel_url=request.build_absolute_uri('/subscription/'),
            client_reference_id=request.user.id,
            metadata={
                'user_id': request.user.id,
                'username': request.user.username
            }
        )

        print(f"Session de paiement créée: {checkout_session.id}")

        # Enregistrer l'ID de session dans la base de données pour référence future
        SubscriptionEvent.objects.create(
            subscription=None,  # Sera mis à jour après la création de l'abonnement
            event_type='checkout_session_created',
            data={
                'session_id': checkout_session.id,
                'customer_id': stripe_customer_id,
                'user_id': request.user.id,
                'timestamp': timezone.now().isoformat()
            }
        )

        return JsonResponse({'id': checkout_session.id})
    except Exception as e:
        print(f"Erreur lors de la création de la session de paiement: {str(e)}")
        return JsonResponse({'error': str(e)}, status=400)

@login_required
def subscription_success(request):
    """Page de succès après abonnement"""
    user_profile = request.user.profile

    # Forcer la récupération des données les plus récentes
    user_profile.refresh_from_db()

    # Vérifier directement auprès de Stripe si l'utilisateur a un abonnement actif
    stripe_subscription_active = False

    if user_profile.stripe_customer_id:
        try:
            # Configurer Stripe avec la clé secrète
            stripe.api_key = settings.STRIPE_SECRET_KEY

            # Récupérer les abonnements du client
            stripe_subscriptions = stripe.Subscription.list(
                customer=user_profile.stripe_customer_id,
                status='active',
                limit=1
            )

            if stripe_subscriptions and stripe_subscriptions.data:
                stripe_subscription = stripe_subscriptions.data[0]
                stripe_subscription_active = True

                # Si l'utilisateur a un abonnement actif dans Stripe mais pas dans notre base de données
                if not user_profile.is_premium:
                    print(f"[subscription_success] Abonnement Stripe actif détecté pour {request.user.username}, mise à jour du profil")

                    # Mettre à jour le profil utilisateur
                    user_profile.is_premium = True
                    user_profile.subscription_active = True
                    user_profile.subscription_id = stripe_subscription.id
                    user_profile.save()

                    # Forcer la récupération des données mises à jour
                    user_profile.refresh_from_db()
        except Exception as e:
            print(f"[subscription_success] Erreur lors de la vérification Stripe: {str(e)}")

    # Vérifier si l'utilisateur a un abonnement actif
    if not user_profile.is_premium and not stripe_subscription_active:
        messages.warning(request, "Votre abonnement est en cours de traitement. Il sera activé sous peu.")
    else:
        messages.success(request, "Félicitations ! Votre abonnement Premium est maintenant actif.")

    # Récupérer les informations d'abonnement pour les afficher
    subscriptions = Subscription.objects.filter(user=request.user).order_by('-created_at')

    context = {
        'user_profile': user_profile,
        'subscriptions': subscriptions,
        'stripe_dashboard_url': settings.STRIPE_DASHBOARD_URL,
        'stripe_subscription_active': stripe_subscription_active
    }

    return render(request, 'subscription_success.html', context)

@login_required
def check_premium_status(request):
    """API pour vérifier le statut premium de l'utilisateur"""
    user_profile = request.user.profile

    # Forcer la récupération des données les plus récentes
    user_profile.refresh_from_db()

    # Récupérer les informations d'abonnement les plus récentes
    subscription = Subscription.objects.filter(user=request.user).order_by('-created_at').first()

    # Ajouter des logs pour le débogage
    print(f"[check_premium_status] Utilisateur: {request.user.username}, Premium: {user_profile.is_premium}")
    if subscription:
        print(f"[check_premium_status] Abonnement en base de données: {subscription.stripe_subscription_id}, Statut: {subscription.status}")

    # Vérifier directement auprès de Stripe si l'utilisateur a un abonnement actif
    stripe_subscription_status = None
    stripe_subscription_id = None

    try:
        # Configurer Stripe avec la clé secrète
        stripe.api_key = settings.STRIPE_SECRET_KEY

        # Vérifier si l'utilisateur a un ID client Stripe
        if user_profile.stripe_customer_id:
            print(f"[check_premium_status] Vérification des abonnements Stripe pour le client: {user_profile.stripe_customer_id}")

            # Récupérer les abonnements du client
            stripe_subscriptions = stripe.Subscription.list(
                customer=user_profile.stripe_customer_id,
                status='active',
                limit=1
            )

            if stripe_subscriptions and stripe_subscriptions.data:
                stripe_subscription = stripe_subscriptions.data[0]
                stripe_subscription_id = stripe_subscription.id
                stripe_subscription_status = stripe_subscription.status
                print(f"[check_premium_status] Abonnement Stripe trouvé: {stripe_subscription_id}, Statut: {stripe_subscription_status}")

                # Vérifier si l'abonnement est actif dans Stripe mais pas dans notre base de données
                if stripe_subscription_status == 'active' and not user_profile.is_premium:
                    print(f"[check_premium_status] Incohérence détectée: abonnement Stripe actif mais profil non premium")

                    # Mettre à jour le profil utilisateur
                    user_profile.is_premium = True
                    user_profile.subscription_active = True
                    user_profile.subscription_id = stripe_subscription_id
                    user_profile.save()

                    # Mettre à jour ou créer l'enregistrement d'abonnement
                    if subscription and subscription.stripe_subscription_id == stripe_subscription_id:
                        subscription.status = 'active'
                        subscription.save()
                    else:
                        # Créer un nouvel enregistrement d'abonnement
                        current_period_end = datetime.datetime.fromtimestamp(stripe_subscription.current_period_end)
                        new_subscription = Subscription.objects.create(
                            user=request.user,
                            stripe_subscription_id=stripe_subscription_id,
                            stripe_customer_id=user_profile.stripe_customer_id,
                            status='active',
                            plan_id=settings.STRIPE_PRICE_ID,
                            start_date=timezone.now(),
                            end_date=current_period_end
                        )
                        print(f"[check_premium_status] Nouvel abonnement créé: {new_subscription.stripe_subscription_id}")

                    print(f"[check_premium_status] Profil mis à jour: {user_profile.user.username} est maintenant premium")
            else:
                print(f"[check_premium_status] Aucun abonnement actif trouvé dans Stripe pour le client: {user_profile.stripe_customer_id}")

                # Si l'utilisateur est marqué comme premium mais n'a pas d'abonnement actif dans Stripe
                if user_profile.is_premium and not subscription:
                    print(f"[check_premium_status] Vérification supplémentaire: l'utilisateur est premium mais n'a pas d'abonnement en base de données")

                    # Vérifier s'il y a des abonnements inactifs
                    inactive_subscriptions = stripe.Subscription.list(
                        customer=user_profile.stripe_customer_id,
                        limit=5
                    )

                    if inactive_subscriptions and inactive_subscriptions.data:
                        for sub in inactive_subscriptions.data:
                            print(f"[check_premium_status] Abonnement Stripe trouvé (inactif): {sub.id}, Statut: {sub.status}")
    except Exception as e:
        print(f"[check_premium_status] Erreur lors de la vérification Stripe: {str(e)}")

    # Vérifier si l'utilisateur a un abonnement actif en base de données mais que son profil n'est pas marqué comme premium
    if subscription and subscription.status == 'active' and not user_profile.is_premium:
        print(f"[check_premium_status] Incohérence détectée: abonnement actif en base de données mais profil non premium")
        # Mettre à jour le profil utilisateur
        user_profile.is_premium = True
        user_profile.subscription_active = True
        user_profile.save()
        print(f"[check_premium_status] Profil mis à jour: {user_profile.user.username} est maintenant premium")

    # Construire la réponse
    response_data = {
        'is_premium': user_profile.is_premium,
        'subscription_active': user_profile.subscription_active if hasattr(user_profile, 'subscription_active') else False,
        'subscription_id': subscription.stripe_subscription_id if subscription else stripe_subscription_id,
        'subscription_status': subscription.status if subscription else stripe_subscription_status,
        'timestamp': timezone.now().timestamp(),
        'username': request.user.username,
        'debug_info': {
            'profile_id': user_profile.id,
            'has_subscription_db': subscription is not None,
            'has_subscription_stripe': stripe_subscription_id is not None,
            'stripe_customer_id': user_profile.stripe_customer_id
        }
    }

    print(f"[check_premium_status] Réponse: {response_data}")
    return JsonResponse(response_data)

@login_required
def subscription_cancel(request):
    """Page d'annulation d'abonnement"""
    messages.warning(request, "Le processus d'abonnement a été annulé. Vous pouvez réessayer à tout moment.")
    return redirect('subscription')

@login_required
@csrf_exempt
def cancel_subscription(request):
    """Annule l'abonnement actif de l'utilisateur"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)

    try:
        # Configurer Stripe avec la clé secrète
        stripe.api_key = settings.STRIPE_SECRET_KEY

        # Récupérer l'abonnement actif de l'utilisateur
        subscription = Subscription.objects.filter(
            user=request.user,
            status='active'
        ).first()

        if not subscription:
            return JsonResponse({'error': 'Aucun abonnement actif trouvé'}, status=404)

        # Annuler l'abonnement dans Stripe
        if subscription.stripe_subscription_id and not subscription.stripe_subscription_id.startswith('manual_'):
            # Utiliser la méthode modify au lieu de save (qui est obsolète)
            stripe.Subscription.modify(
                subscription.stripe_subscription_id,
                cancel_at_period_end=True
            )

            # Mettre à jour l'abonnement dans la base de données
            subscription.status = 'canceled'
            subscription.save()

            # Enregistrer l'événement
            SubscriptionEvent.objects.create(
                subscription=subscription,
                event_type='canceled_by_user',
                data={"message": "Abonnement annulé par l'utilisateur"}
            )

            # L'utilisateur reste premium jusqu'à la fin de la période
            messages.info(request, "Votre abonnement a été annulé. Vous conserverez l'accès premium jusqu'à la fin de la période en cours.")
        else:
            # Abonnement manuel ou sans ID Stripe
            subscription.status = 'canceled'
            subscription.save()

            # Mettre à jour le profil utilisateur
            user_profile = request.user.profile
            user_profile.is_premium = False
            user_profile.subscription_active = False
            user_profile.save()

            # Enregistrer l'événement
            SubscriptionEvent.objects.create(
                subscription=subscription,
                event_type='canceled_by_user',
                data={"message": "Abonnement annulé par l'utilisateur"}
            )

            messages.info(request, "Votre abonnement a été annulé immédiatement.")

        return JsonResponse({'success': True})
    except Exception as e:
        print(f"Erreur lors de l'annulation de l'abonnement: {str(e)}")
        return JsonResponse({'error': str(e)}, status=400)

# Vues administrateur
@login_required
def admin_dashboard(request):
    """Tableau de bord administrateur"""
    if not request.user.is_staff:
        messages.error(request, "Vous n'avez pas les droits pour accéder à cette page.")
        return redirect('home')

    # Statistiques générales
    total_users = UserProfile.objects.count()
    premium_users = UserProfile.objects.filter(is_premium=True).count()
    free_users = total_users - premium_users
    total_searches = Search.objects.count()

    # Liste des utilisateurs
    users = UserProfile.objects.all().order_by('-is_premium', 'user__date_joined')

    context = {
        'total_users': total_users,
        'premium_users': premium_users,
        'free_users': free_users,
        'total_searches': total_searches,
        'users': users
    }

    return render(request, 'admin/dashboard.html', context)

@login_required
def admin_user_detail(request, user_id):
    """Détail d'un utilisateur pour l'administrateur"""
    if not request.user.is_staff:
        messages.error(request, "Vous n'avez pas les droits pour accéder à cette page.")
        return redirect('home')

    user_profile = get_object_or_404(UserProfile, user_id=user_id)
    searches = Search.objects.filter(user=user_profile.user).order_by('-timestamp')
    subscriptions = Subscription.objects.filter(user=user_profile.user).order_by('-created_at')

    # Traiter l'action de mise à jour du statut premium
    if request.method == 'POST' and 'action' in request.POST:
        action = request.POST.get('action')

        if action == 'make_premium':
            # Mettre à jour l'utilisateur en mode premium
            user_profile.is_premium = True
            user_profile.subscription_active = True
            user_profile.subscription_start_date = timezone.now()
            user_profile.subscription_end_date = timezone.now() + datetime.timedelta(days=30)
            user_profile.save()

            # Créer un enregistrement d'abonnement
            subscription = Subscription.objects.create(
                user=user_profile.user,
                stripe_subscription_id=f"manual_{timezone.now().timestamp()}",
                stripe_customer_id=f"manual_{user_profile.user.id}",
                status='active',
                plan_id=settings.STRIPE_PRICE_ID,
                start_date=timezone.now(),
                end_date=timezone.now() + datetime.timedelta(days=30)
            )

            # Enregistrer l'événement
            SubscriptionEvent.objects.create(
                subscription=subscription,
                event_type='created_manually',
                data={"message": "Abonnement créé manuellement par l'administrateur"}
            )

            messages.success(request, f"L'utilisateur {user_profile.user.username} est maintenant en mode Premium.")
            return redirect('is_admin_user_detail', user_id=user_id)

        elif action == 'remove_premium':
            # Retirer le statut premium
            user_profile.is_premium = False
            user_profile.subscription_active = False
            user_profile.save()

            # Mettre à jour les abonnements actifs
            active_subscriptions = Subscription.objects.filter(
                user=user_profile.user,
                status='active'
            )

            for subscription in active_subscriptions:
                subscription.status = 'canceled'
                subscription.save()

                # Enregistrer l'événement
                SubscriptionEvent.objects.create(
                    subscription=subscription,
                    event_type='canceled_manually',
                    data={"message": "Abonnement annulé manuellement par l'administrateur"}
                )

            messages.success(request, f"Le statut Premium a été retiré de l'utilisateur {user_profile.user.username}.")
            return redirect('is_admin_user_detail', user_id=user_id)

    context = {
        'user_profile': user_profile,
        'searches': searches,
        'subscriptions': subscriptions,
        'stripe_dashboard_url': 'https://dashboard.stripe.com/test/dashboard'
    }

    return render(request, 'admin/user_detail.html', context)

# Webhook Stripe
@csrf_exempt
def stripe_webhook(request):
    """Webhook pour les événements Stripe"""
    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')

    # Configurer Stripe avec la clé secrète
    stripe.api_key = settings.STRIPE_SECRET_KEY

    try:
        # Vérifier la signature du webhook
        event = stripe.Webhook.construct_event(
            payload, sig_header, settings.STRIPE_WEBHOOK_SECRET
        )
        print(f"Événement Stripe reçu: {event['type']}")
    except ValueError as e:
        print(f"Erreur de décodage du payload: {str(e)}")
        return HttpResponse(status=400)
    except stripe.error.SignatureVerificationError as e:
        print(f"Erreur de vérification de signature: {str(e)}")
        return HttpResponse(status=400)

    # Gérer les événements
    try:
        event_type = event['type']
        event_data = event['data']['object']

        if event_type == 'checkout.session.completed':
            print("Traitement de l'événement checkout.session.completed")
            handle_checkout_session(event_data)
        elif event_type == 'customer.subscription.updated':
            print("Traitement de l'événement customer.subscription.updated")
            handle_subscription_updated(event_data)
        elif event_type == 'customer.subscription.deleted':
            print("Traitement de l'événement customer.subscription.deleted")
            handle_subscription_deleted(event_data)
        elif event_type == 'invoice.payment_succeeded':
            print("Traitement de l'événement invoice.payment_succeeded")
            # Vous pouvez ajouter une fonction pour gérer les paiements réussis
            # handle_payment_succeeded(event_data)
        elif event_type == 'invoice.payment_failed':
            print("Traitement de l'événement invoice.payment_failed")
            # Vous pouvez ajouter une fonction pour gérer les échecs de paiement
            # handle_payment_failed(event_data)
        else:
            print(f"Événement non géré: {event_type}")
    except Exception as e:
        print(f"Erreur lors du traitement de l'événement: {str(e)}")
        # Continuer à retourner 200 pour éviter que Stripe ne réessaie

    # Toujours retourner un 200 pour que Stripe sache que l'événement a été reçu
    return HttpResponse(status=200)

# Fonctions utilitaires
def get_trend_data(query, start_date=None, end_date=None, country=None):
    """
    Récupère les données de tendance via SerpAPI avec gestion améliorée des erreurs et fallback

    Args:
        query (str): La requête de recherche
        start_date (str, optional): Date de début au format 'YYYY-MM-DD'
        end_date (str, optional): Date de fin au format 'YYYY-MM-DD'
        country (str, optional): Code du pays (ex: 'FR', 'US', etc.)

    Returns:
        dict: Données de tendance complètes (API ou démonstration)
    """
    from core.utils.serper import search_trend
    from core.demo_data import generate_demo_data
    import time

    # Nombre maximum de tentatives
    max_attempts = 2
    current_attempt = 0

    while current_attempt < max_attempts:
        try:
            print(f"Tentative {current_attempt + 1}/{max_attempts} de récupération des données pour la requête: {query}")
            print(f"Paramètres: start_date={start_date}, end_date={end_date}, country={country}")

            # Utiliser la fonction search_trend de serper.py avec les paramètres
            trend_data = search_trend(query, start_date, end_date, country)

            # Vérifier si le fallback est explicitement demandé
            if isinstance(trend_data, dict) and trend_data.get('fallback_needed', False):
                print("Fallback explicitement demandé par l'API")
                return generate_demo_data(query, start_date, end_date, country)

            # Vérifier si une erreur est survenue
            if isinstance(trend_data, dict) and 'error' in trend_data:
                print(f"Erreur lors de l'appel à SerpAPI: {trend_data['error']}")

                # Si c'est la dernière tentative, utiliser les données de démonstration
                if current_attempt == max_attempts - 1:
                    print("Utilisation des données de démonstration comme fallback")
                    return generate_demo_data(query, start_date, end_date, country)

                # Sinon, réessayer après une courte pause
                current_attempt += 1
                time.sleep(1)
                continue

            # Vérifier si les données sont vides ou incomplètes
            required_keys = ['interest_over_time', 'interest_by_region', 'related_topics', 'related_queries']
            missing_keys = [key for key in required_keys if key not in trend_data]

            if missing_keys:
                print(f"Données incomplètes reçues de SerpAPI. Clés manquantes: {missing_keys}")

                # Si c'est la dernière tentative, utiliser les données de démonstration
                if current_attempt == max_attempts - 1:
                    print("Utilisation des données de démonstration comme fallback")
                    return generate_demo_data(query, start_date, end_date, country)

                # Sinon, réessayer après une courte pause
                current_attempt += 1
                time.sleep(1)
                continue

            # Vérifier si les données sont vides ou insuffisantes
            empty_sections = []
            if not trend_data.get('interest_over_time'):
                empty_sections.append('interest_over_time')
            if not trend_data.get('interest_by_region'):
                empty_sections.append('interest_by_region')
            if not trend_data.get('related_topics') or not any(trend_data['related_topics'].values()):
                empty_sections.append('related_topics')
            if not trend_data.get('related_queries') or not any(trend_data['related_queries'].values()):
                empty_sections.append('related_queries')

            if empty_sections:
                print(f"Sections vides dans les données: {empty_sections}")

                # Si c'est la dernière tentative, utiliser les données de démonstration
                if current_attempt == max_attempts - 1:
                    print("Utilisation des données de démonstration comme fallback")

                    # Créer un mélange de données réelles et de démonstration
                    demo_data = generate_demo_data(query, start_date, end_date, country)

                    # Remplacer uniquement les sections vides par des données de démonstration
                    for section in empty_sections:
                        trend_data[section] = demo_data[section]

                    print("Données hybrides (API + démo) créées avec succès")
                    return trend_data

                # Sinon, réessayer après une courte pause
                current_attempt += 1
                time.sleep(1)
                continue

            print("Données récupérées avec succès de SerpAPI")
            return trend_data

        except Exception as e:
            print(f"Exception lors de l'appel à SerpAPI: {str(e)}")

            # Si c'est la dernière tentative, utiliser les données de démonstration
            if current_attempt == max_attempts - 1:
                print("Utilisation des données de démonstration comme fallback")
                return generate_demo_data(query, start_date, end_date, country)

            # Sinon, réessayer après une courte pause
            current_attempt += 1
            time.sleep(1)
            continue

    # Si toutes les tentatives ont échoué (ne devrait jamais arriver ici)
    print("Toutes les tentatives ont échoué, utilisation des données de démonstration")
    return generate_demo_data(query, start_date, end_date, country)

def generate_interest_over_time(query):
    """Génère des données simulées d'intérêt au fil du temps"""
    interest_data = {}
    today = datetime.datetime.now()

    # Générer des données pour les 12 derniers mois
    for i in range(365, 0, -1):
        date = today - timedelta(days=i)
        date_str = date.strftime('%Y-%m-%d')

        # Générer une valeur aléatoire entre 20 et 80
        base_value = random.randint(20, 80)

        # Ajouter des pics pour simuler des tendances
        if i % 30 < 5:  # Pic tous les mois
            base_value += random.randint(10, 20)

        interest_data[date_str] = min(100, base_value)  # Limiter à 100

    return interest_data

def generate_interest_by_region(query):
    """Génère des données simulées d'intérêt par région"""
    regions = [
        "Île-de-France", "Auvergne-Rhône-Alpes", "Nouvelle-Aquitaine",
        "Occitanie", "Hauts-de-France", "Grand Est", "Provence-Alpes-Côte d'Azur",
        "Pays de la Loire", "Normandie", "Bretagne", "Bourgogne-Franche-Comté",
        "Centre-Val de Loire", "Corse"
    ]

    interest_data = {}
    for region in regions:
        interest_data[region] = random.randint(30, 100)

    return interest_data

def extract_related_queries(search_results):
    """Extrait les requêtes associées des résultats de recherche"""
    related_queries = {
        'top': [],
        'rising': []
    }

    # Extraire les requêtes associées des résultats de recherche
    if 'relatedSearches' in search_results:
        for i, query in enumerate(search_results['relatedSearches'][:10]):
            related_queries['top'].append({
                'query': query,
                'value': 100 - (i * 10)  # Valeur décroissante
            })

    # Générer des requêtes "rising" simulées
    if 'relatedSearches' in search_results:
        for i, query in enumerate(search_results['relatedSearches'][:5]):
            related_queries['rising'].append({
                'query': f"Nouveau {query}",
                'value': random.randint(100, 5000)
            })

    return related_queries

def extract_related_topics(search_results):
    """Extrait les sujets associés des résultats de recherche"""
    related_topics = {
        'top': [],
        'rising': []
    }

    # Extraire les sujets associés des résultats de recherche
    if 'knowledgeGraph' in search_results and 'attributes' in search_results['knowledgeGraph']:
        attributes = search_results['knowledgeGraph']['attributes']
        for i, (key, value) in enumerate(attributes.items()):
            if i < 10:
                related_topics['top'].append({
                    'topic': key,
                    'value': value,
                    'formattedValue': value
                })

    # Utiliser les résultats organiques comme sujets associés si nécessaire
    if len(related_topics['top']) < 5 and 'organic' in search_results:
        for i, result in enumerate(search_results['organic'][:10]):
            if 'title' in result:
                related_topics['top'].append({
                    'topic': result['title'],
                    'value': 100 - (i * 10),
                    'formattedValue': f"{100 - (i * 10)}%"
                })

    # Générer des sujets "rising" simulés
    for i in range(5):
        if 'organic' in search_results and i < len(search_results['organic']):
            title = search_results['organic'][i].get('title', f"Sujet tendance {i+1}")
            related_topics['rising'].append({
                'topic': title,
                'value': random.randint(100, 5000),
                'formattedValue': f"+{random.randint(100, 5000)}%"
            })

    return related_topics

def generate_fallback_data(query):
    """Génère des données de secours en cas d'échec de l'API"""
    return {
        'interest_over_time': generate_interest_over_time(query),
        'interest_by_region': generate_interest_by_region(query),
        'related_queries': {
            'top': [{'query': f"Comment {query}", 'value': 100},
                   {'query': f"{query} avis", 'value': 90},
                   {'query': f"{query} France", 'value': 80},
                   {'query': f"{query} prix", 'value': 70},
                   {'query': f"{query} comparatif", 'value': 60}],
            'rising': [{'query': f"Nouveau {query}", 'value': 5000},
                      {'query': f"{query} 2025", 'value': 4000},
                      {'query': f"{query} tendance", 'value': 3000},
                      {'query': f"{query} innovation", 'value': 2000},
                      {'query': f"{query} actualité", 'value': 1000}]
        },
        'related_topics': {
            'top': [{'topic': f"{query} technologie", 'value': 100, 'formattedValue': '100%'},
                   {'topic': f"{query} marque", 'value': 90, 'formattedValue': '90%'},
                   {'topic': f"{query} entreprise", 'value': 80, 'formattedValue': '80%'},
                   {'topic': f"{query} produit", 'value': 70, 'formattedValue': '70%'},
                   {'topic': f"{query} service", 'value': 60, 'formattedValue': '60%'}],
            'rising': [{'topic': f"Nouveau {query}", 'value': 5000, 'formattedValue': '+5000%'},
                      {'topic': f"{query} innovation", 'value': 4000, 'formattedValue': '+4000%'},
                      {'topic': f"{query} tendance", 'value': 3000, 'formattedValue': '+3000%'},
                      {'topic': f"{query} futur", 'value': 2000, 'formattedValue': '+2000%'},
                      {'topic': f"{query} révolution", 'value': 1000, 'formattedValue': '+1000%'}]
        }
    }

def handle_checkout_session(session):
    """Gère un événement de session de paiement réussie"""
    user_id = session.get('client_reference_id')
    if not user_id:
        print("Aucun client_reference_id trouvé dans la session")
        # Essayer de récupérer l'ID utilisateur à partir des métadonnées
        if session.get('metadata') and session.get('metadata').get('user_id'):
            user_id = session.get('metadata').get('user_id')
            print(f"ID utilisateur récupéré à partir des métadonnées: {user_id}")
        else:
            return

    try:
        # Récupérer les informations complètes sur l'abonnement
        stripe.api_key = settings.STRIPE_SECRET_KEY
        subscription_id = session.get('subscription')

        if not subscription_id:
            print("Aucun ID d'abonnement trouvé dans la session")
            return

        # Récupérer les détails de l'abonnement depuis Stripe
        stripe_subscription = stripe.Subscription.retrieve(subscription_id)

        # Calculer la date de fin de l'abonnement
        current_period_end = datetime.datetime.fromtimestamp(stripe_subscription.current_period_end)

        # Mettre à jour le profil utilisateur
        user_profile = UserProfile.objects.get(user_id=user_id)

        # Mettre à jour les informations d'abonnement
        user_profile.is_premium = True
        user_profile.subscription_active = True
        user_profile.subscription_id = subscription_id
        user_profile.stripe_customer_id = session.get('customer')
        user_profile.subscription_start_date = timezone.now()
        user_profile.subscription_end_date = current_period_end
        user_profile.save()

        print(f"Profil utilisateur mis à jour: {user_profile.user.username} est maintenant premium")

        # Vérifier si un abonnement existe déjà pour cet utilisateur
        existing_subscription = Subscription.objects.filter(
            user=user_profile.user,
            stripe_subscription_id=subscription_id
        ).first()

        if existing_subscription:
            # Mettre à jour l'abonnement existant
            existing_subscription.status = stripe_subscription.status
            existing_subscription.start_date = timezone.now()
            existing_subscription.end_date = current_period_end
            existing_subscription.save()
            subscription = existing_subscription
            print(f"Abonnement existant mis à jour: {subscription.stripe_subscription_id}")
        else:
            # Créer un nouvel enregistrement d'abonnement
            subscription = Subscription.objects.create(
                user=user_profile.user,
                stripe_subscription_id=subscription_id,
                stripe_customer_id=session.get('customer'),
                status=stripe_subscription.status,
                plan_id=settings.STRIPE_PRICE_ID,
                start_date=timezone.now(),
                end_date=current_period_end
            )
            print(f"Nouvel abonnement créé: {subscription.stripe_subscription_id}")

        # Enregistrer l'événement
        event_data = {
            'session_id': session.get('id'),
            'customer_id': session.get('customer'),
            'subscription_id': subscription_id,
            'payment_status': session.get('payment_status'),
            'amount_total': session.get('amount_total'),
            'timestamp': timezone.now().isoformat()
        }

        # Mettre à jour l'événement de création de session s'il existe
        previous_event = SubscriptionEvent.objects.filter(
            event_type='checkout_session_created',
            data__session_id=session.get('id')
        ).first()

        if previous_event:
            previous_event.subscription = subscription
            previous_event.save()
            print(f"Événement de création de session mis à jour avec l'abonnement: {subscription.id}")

        # Créer un nouvel événement pour la session complétée
        SubscriptionEvent.objects.create(
            subscription=subscription,
            event_type='checkout.session.completed',
            data=event_data
        )

        print(f"Événement d'abonnement enregistré: checkout.session.completed")

        # Forcer la mise à jour du cache de session pour que les changements soient immédiatement visibles
        from django.contrib.auth.models import User
        user = User.objects.get(id=user_id)
        user.profile.refresh_from_db()

        # Vérifier que le statut premium est correctement défini
        if not user.profile.is_premium:
            print(f"ATTENTION: Le statut premium n'est pas correctement défini pour l'utilisateur {user.username}")
            user.profile.is_premium = True
            user.profile.save()
            print(f"Statut premium forcé pour l'utilisateur {user.username}")

    except UserProfile.DoesNotExist:
        print(f"Profil utilisateur non trouvé pour l'ID: {user_id}")
    except Exception as e:
        print(f"Erreur lors du traitement de la session de paiement: {str(e)}")

def handle_subscription_updated(subscription_data):
    """Gère un événement de mise à jour d'abonnement"""
    subscription_id = subscription_data.get('id')
    try:
        subscription = Subscription.objects.get(stripe_subscription_id=subscription_id)

        # Mettre à jour l'abonnement
        subscription.status = subscription_data.get('status')
        if subscription_data.get('current_period_end'):
            end_date = datetime.datetime.fromtimestamp(subscription_data.get('current_period_end'))
            subscription.end_date = end_date

            # Mettre à jour le profil utilisateur
            user_profile = subscription.user.profile
            user_profile.subscription_end_date = end_date
            user_profile.save()

        subscription.save()

        # Enregistrer l'événement
        SubscriptionEvent.objects.create(
            subscription=subscription,
            event_type='updated',
            data=subscription_data
        )
    except Subscription.DoesNotExist:
        pass

def handle_subscription_deleted(subscription_data):
    """Gère un événement de suppression d'abonnement"""
    subscription_id = subscription_data.get('id')
    try:
        subscription = Subscription.objects.get(stripe_subscription_id=subscription_id)

        # Mettre à jour l'abonnement
        subscription.status = 'canceled'
        subscription.save()

        # Mettre à jour le profil utilisateur
        user_profile = subscription.user.profile
        user_profile.is_premium = False
        user_profile.subscription_active = False
        user_profile.save()

        # Enregistrer l'événement
        SubscriptionEvent.objects.create(
            subscription=subscription,
            event_type='deleted',
            data=subscription_data
        )
    except Subscription.DoesNotExist:
        pass



