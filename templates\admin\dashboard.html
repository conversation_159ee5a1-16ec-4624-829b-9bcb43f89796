{% extends 'admin/base_admin.html' %}
{% load core_extras %}

{% block admin_title %}Tableau de bord administrateur{% endblock %}

{% block admin_extra_css %}
<style>
    /* Enhanced Admin Dashboard Styles */
    .admin-dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 24px;
        padding: 3.5rem 3rem;
        margin-bottom: 3rem;
        position: relative;
        overflow: hidden;
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.2);
    }

    .admin-dashboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="2"/></svg>');
        background-size: 120px 120px;
        opacity: 0.4;
    }

    .admin-dashboard-header h1 {
        position: relative;
        z-index: 2;
        margin: 0;
        font-weight: 800;
        font-size: 2.5rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        letter-spacing: -0.025em;
    }

    .admin-dashboard-header p {
        position: relative;
        z-index: 2;
        margin: 0;
        opacity: 0.95;
        font-size: 1.2rem;
        font-weight: 400;
    }

    /* Enhanced Stats Cards - Modern Visual Design */
    .admin-stats-card {
        background: white;
        border-radius: 24px;
        padding: 3rem 2.5rem;
        box-shadow: 0 15px 40px rgba(0,0,0,0.12);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border: 3px solid #f1f5f9;
        position: relative;
        overflow: hidden;
        height: 100%;
    }

    .admin-stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        transform: scaleX(0);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .admin-stats-card::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle at top right, rgba(102, 126, 234, 0.05), transparent 50%);
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .admin-stats-card:hover::before {
        transform: scaleX(1);
    }

    .admin-stats-card:hover::after {
        opacity: 1;
    }

    .admin-stats-card:hover {
        transform: translateY(-15px) scale(1.04);
        box-shadow: 0 30px 60px rgba(0,0,0,0.2);
        border-color: #e2e8f0;
    }

    .admin-stats-icon {
        width: 80px;
        height: 80px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.2rem;
        margin-bottom: 2rem;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        position: relative;
        z-index: 2;
    }

    .admin-stats-card:hover .admin-stats-icon {
        transform: scale(1.2) rotate(10deg);
        box-shadow: 0 12px 35px rgba(0,0,0,0.25);
    }

    .users-icon {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }

    .premium-icon {
        background: linear-gradient(135deg, #22c55e, #16a34a);
        color: white;
    }

    .searches-icon {
        background: linear-gradient(135deg, #f093fb, #f5576c);
        color: white;
    }

    .admin-stats-number {
        font-size: 3.5rem;
        font-weight: 900;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 1rem;
        line-height: 0.9;
        position: relative;
        z-index: 2;
    }

    .admin-stats-label {
        font-size: 1.1rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 0.5rem;
        position: relative;
        z-index: 2;
    }

    .admin-stats-description {
        font-size: 0.95rem;
        font-weight: 500;
        color: #64748b;
        line-height: 1.4;
        position: relative;
        z-index: 2;
    }

    /* Enhanced Table Styles - Reinforced Borders */
    .admin-table-card {
        background: white;
        border-radius: 24px;
        box-shadow: 0 15px 40px rgba(0,0,0,0.12);
        border: 4px solid #e2e8f0;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        margin-bottom: 2.5rem;
        position: relative;
    }

    .admin-table-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        z-index: 1;
    }

    .admin-table-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 30px 60px rgba(0,0,0,0.18);
        border-color: #cbd5e1;
    }

    .admin-table-header {
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        border: none;
        padding: 3rem 3.5rem 2rem;
        border-bottom: 4px solid #d1d5db;
        position: relative;
    }

    .admin-table-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 3.5rem;
        right: 3.5rem;
        height: 2px;
        background: linear-gradient(90deg, transparent, #667eea, transparent);
    }

    .admin-table-header h5 {
        margin: 0;
        font-weight: 800;
        color: #1e293b;
        font-size: 1.75rem;
        letter-spacing: -0.025em;
        line-height: 1.2;
    }

    .admin-table-header p {
        margin: 1rem 0 0;
        color: #64748b;
        font-size: 1.15rem;
        font-weight: 500;
        line-height: 1.4;
    }

    .admin-table {
        margin: 0;
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        font-size: 1.1rem;
    }

    .admin-table thead th {
        background: linear-gradient(135deg, #f8fafc, #f1f5f9);
        border: none;
        font-weight: 800;
        color: #374151;
        padding: 2.25rem 3rem;
        border-bottom: 4px solid #e5e7eb;
        border-right: 3px solid #f3f4f6;
        font-size: 1.1rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        position: sticky;
        top: 0;
        z-index: 10;
        text-align: left;
        line-height: 1.3;
    }

    .admin-table thead th:last-child {
        border-right: none;
    }

    .admin-table thead th:first-child {
        border-left: 4px solid transparent;
    }

    .admin-table tbody tr {
        transition: all 0.3s ease;
        border: none;
        background: white;
    }

    .admin-table tbody tr:hover {
        background: linear-gradient(135deg, rgba(67, 97, 238, 0.04), rgba(67, 97, 238, 0.08));
        transform: scale(1.008);
        box-shadow: 0 6px 20px rgba(67, 97, 238, 0.12);
    }

    .admin-table tbody tr:nth-child(even) {
        background: rgba(248, 250, 252, 0.6);
    }

    .admin-table tbody tr:nth-child(even):hover {
        background: linear-gradient(135deg, rgba(67, 97, 238, 0.04), rgba(67, 97, 238, 0.08));
    }

    .admin-table tbody td {
        padding: 2.25rem 3rem;
        border: none;
        vertical-align: middle;
        border-bottom: 3px solid #e5e7eb;
        border-right: 3px solid #f3f4f6;
        font-size: 1.1rem;
        line-height: 1.6;
        font-weight: 500;
    }

    .admin-table tbody td:last-child {
        border-right: none;
    }

    .admin-table tbody td:first-child {
        border-left: 4px solid transparent;
    }

    .admin-table tbody tr:hover td:first-child {
        border-left-color: #667eea;
    }

    .admin-table tbody tr:last-child td {
        border-bottom: none;
    }

    /* Enhanced User Info Components */
    .user-avatar {
        width: 50px;
        height: 50px;
        border-radius: 14px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 800;
        font-size: 1.2rem;
        margin-right: 1.5rem;
        flex-shrink: 0;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .user-info {
        display: flex;
        align-items: center;
        min-width: 0;
    }

    .user-details {
        min-width: 0;
        flex: 1;
    }

    .user-name {
        font-weight: 700;
        color: #1e293b;
        margin: 0 0 0.25rem 0;
        font-size: 1.1rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.3;
    }

    .user-role {
        color: #64748b;
        font-size: 0.9rem;
        margin: 0;
        font-weight: 500;
    }

    .search-query {
        font-weight: 700;
        color: #667eea;
        background: rgba(102, 126, 234, 0.12);
        padding: 0.6rem 1rem;
        border-radius: 10px;
        display: inline-block;
        font-size: 0.95rem;
        max-width: 200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        border: 1px solid rgba(102, 126, 234, 0.2);
    }

    .date-info {
        text-align: right;
    }

    .date-primary {
        font-weight: 700;
        color: #1e293b;
        margin: 0 0 0.25rem 0;
        font-size: 1rem;
        line-height: 1.3;
    }

    .date-secondary {
        color: #64748b;
        font-size: 0.9rem;
        margin: 0;
        font-weight: 500;
    }

    .empty-state {
        text-align: center;
        padding: 5rem 3rem;
        color: #64748b;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 2rem;
        opacity: 0.5;
        color: #94a3b8;
    }

    .empty-state h6 {
        color: #475569;
        font-weight: 700;
        margin-bottom: 0.75rem;
        font-size: 1.2rem;
    }

    .empty-state p {
        color: #64748b;
        font-size: 1rem;
        font-weight: 500;
    }

    /* Animation Classes */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(40px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in {
        animation: fadeInUp 0.8s ease-out;
    }

    .animate-delay-1 { animation-delay: 0.1s; animation-fill-mode: both; }
    .animate-delay-2 { animation-delay: 0.2s; animation-fill-mode: both; }
    .animate-delay-3 { animation-delay: 0.3s; animation-fill-mode: both; }
    .animate-delay-4 { animation-delay: 0.4s; animation-fill-mode: both; }
</style>
{% endblock %}

{% block admin_content %}
<!-- Enhanced Dashboard Header -->
<div class="admin-dashboard-header animate-fade-in" data-aos="fade-up">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1>Tableau de bord administrateur 🛠️</h1>
            <p class="mb-0">Gérez votre plateforme et surveillez les performances en temps réel avec des outils avancés.</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="d-inline-flex align-items-center">
                <i class="bi bi-calendar3 me-2"></i>
                <span style="font-size: 1.1rem; font-weight: 600;">{{ "now"|date:"d F Y" }}</span>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Stats Cards with Modern Visual Design -->
<div class="row mb-5">
    <div class="col-md-4 animate-fade-in animate-delay-1" data-aos="fade-up" data-aos-delay="100">
        <div class="admin-stats-card">
            <div class="admin-stats-icon users-icon">
                <i class="bi bi-people-fill"></i>
            </div>
            <div class="admin-stats-number">{{ total_users }}</div>
            <div class="admin-stats-label">Utilisateurs inscrits</div>
            <div class="admin-stats-description">
                <i class="bi bi-graph-up me-1"></i>
                Total des comptes créés
            </div>
        </div>
    </div>

    <div class="col-md-4 animate-fade-in animate-delay-2" data-aos="fade-up" data-aos-delay="200">
        <div class="admin-stats-card">
            <div class="admin-stats-icon premium-icon">
                <i class="bi bi-star-fill"></i>
            </div>
            <div class="admin-stats-number">{{ premium_users }}</div>
            <div class="admin-stats-label">Abonnés premium</div>
            <div class="admin-stats-description">
                <i class="bi bi-currency-dollar me-1"></i>
                Revenus récurrents actifs
            </div>
        </div>
    </div>

    <div class="col-md-4 animate-fade-in animate-delay-3" data-aos="fade-up" data-aos-delay="300">
        <div class="admin-stats-card">
            <div class="admin-stats-icon searches-icon">
                <i class="bi bi-search"></i>
            </div>
            <div class="admin-stats-number">{{ total_searches }}</div>
            <div class="admin-stats-label">Recherches effectuées</div>
            <div class="admin-stats-description">
                <i class="bi bi-activity me-1"></i>
                Utilisation de la plateforme
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Tables Section with Reinforced Borders -->
<div class="row">
    <!-- Derniers utilisateurs - Enhanced with Strong Borders -->
    <div class="col-12 mb-4 animate-fade-in animate-delay-4" data-aos="fade-up" data-aos-delay="400">
        <div class="admin-table-card">
            <div class="admin-table-header">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h5>Derniers utilisateurs inscrits</h5>
                        <p>Nouveaux comptes créés récemment sur la plateforme</p>
                    </div>
                    <div class="admin-stats-icon users-icon" style="width: 60px; height: 60px; font-size: 1.4rem; margin-bottom: 0;">
                        <i class="bi bi-person-plus"></i>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                {% if recent_users %}
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th style="width: 40%;"><i class="bi bi-person me-2"></i>Utilisateur</th>
                            <th style="width: 35%;"><i class="bi bi-envelope me-2"></i>Adresse Email</th>
                            <th style="width: 25%;"><i class="bi bi-calendar3 me-2"></i>Date d'inscription</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in recent_users %}
                        <tr>
                            <td>
                                <div class="user-info">
                                    <div class="user-avatar">
                                        {{ user.username|slice:":1"|upper }}
                                    </div>
                                    <div class="user-details">
                                        <p class="user-name">{{ user.username }}</p>
                                        <p class="user-role">Utilisateur standard</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; align-items: center;">
                                    <i class="bi bi-envelope-fill me-2 text-muted" style="font-size: 1rem;"></i>
                                    <span class="text-muted" style="font-weight: 500;">{{ user.email }}</span>
                                </div>
                            </td>
                            <td>
                                <div class="date-info">
                                    <p class="date-primary">{{ user.date_joined|date:"d M Y" }}</p>
                                    <p class="date-secondary">{{ user.date_joined|date:"H:i" }}</p>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% else %}
                <div class="empty-state">
                    <i class="bi bi-person-plus"></i>
                    <h6>Aucun utilisateur récent</h6>
                    <p>Aucune inscription récente à afficher pour le moment.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Dernières recherches - Enhanced with Strong Borders -->
    <div class="col-12 mb-4 animate-fade-in animate-delay-4" data-aos="fade-up" data-aos-delay="500">
        <div class="admin-table-card">
            <div class="admin-table-header">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h5>Dernières recherches effectuées</h5>
                        <p>Activité récente des utilisateurs sur la plateforme d'analyse</p>
                    </div>
                    <div class="admin-stats-icon searches-icon" style="width: 60px; height: 60px; font-size: 1.4rem; margin-bottom: 0;">
                        <i class="bi bi-search"></i>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                {% if recent_searches %}
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th style="width: 40%;"><i class="bi bi-search me-2"></i>Requête de recherche</th>
                            <th style="width: 30%;"><i class="bi bi-person me-2"></i>Utilisateur</th>
                            <th style="width: 30%;"><i class="bi bi-calendar3 me-2"></i>Date et heure</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for search in recent_searches %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="rounded-circle bg-primary bg-opacity-10 d-flex align-items-center justify-content-center me-3" style="width: 45px; height: 45px; flex-shrink: 0;">
                                        <i class="bi bi-search text-primary" style="font-size: 1.1rem;"></i>
                                    </div>
                                    <div style="min-width: 0; flex: 1;">
                                        <span class="search-query" title="{{ search.query }}">{{ search.query }}</span>
                                        <div style="margin-top: 0.25rem;">
                                            <small class="text-muted" style="font-weight: 500;">Analyse de tendances</small>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="user-info">
                                    <div class="user-avatar" style="width: 40px; height: 40px; font-size: 0.9rem;">
                                        {{ search.user.username|slice:":1"|upper }}
                                    </div>
                                    <div class="user-details">
                                        <p class="user-name" style="font-size: 1rem;">{{ search.user.username }}</p>
                                        <p class="user-role">Recherche utilisateur</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="date-info">
                                    <p class="date-primary">{{ search.timestamp|date:"d M Y" }}</p>
                                    <p class="date-secondary">{{ search.timestamp|date:"H:i" }}</p>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% else %}
                <div class="empty-state">
                    <i class="bi bi-search"></i>
                    <h6>Aucune recherche récente</h6>
                    <p>Aucune activité de recherche à afficher pour le moment.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block admin_extra_js %}
<script>
    // Enhanced Animation and Interaction Scripts
    document.addEventListener('DOMContentLoaded', function() {
        // Enhanced number animation with easing
        const numbers = document.querySelectorAll('.admin-stats-number');
        numbers.forEach(number => {
            const finalValue = parseInt(number.textContent);
            if (!isNaN(finalValue) && finalValue > 0) {
                let currentValue = 0;
                const duration = 2000; // 2 seconds
                const increment = finalValue / (duration / 16);

                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        currentValue = finalValue;
                        clearInterval(timer);
                    }
                    number.textContent = Math.floor(currentValue);
                }, 16);
            }
        });

        // Enhanced hover effects for cards
        document.querySelectorAll('.admin-stats-card, .admin-table-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                if (this.classList.contains('admin-stats-card')) {
                    this.style.transform = 'translateY(-15px) scale(1.04)';
                    this.style.boxShadow = '0 30px 60px rgba(0,0,0,0.2)';
                } else {
                    this.style.transform = 'translateY(-10px)';
                    this.style.boxShadow = '0 30px 60px rgba(0,0,0,0.18)';
                }
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
                if (this.classList.contains('admin-stats-card')) {
                    this.style.boxShadow = '0 15px 40px rgba(0,0,0,0.12)';
                } else {
                    this.style.boxShadow = '0 15px 40px rgba(0,0,0,0.12)';
                }
            });
        });

        // Enhanced table row animations with staggered effect
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const rows = entry.target.querySelectorAll('tbody tr');
                    rows.forEach((row, index) => {
                        setTimeout(() => {
                            row.style.opacity = '1';
                            row.style.transform = 'translateY(0)';
                        }, index * 150); // Increased delay for better effect
                    });
                }
            });
        }, observerOptions);

        document.querySelectorAll('.admin-table').forEach(table => {
            // Initialize row states
            table.querySelectorAll('tbody tr').forEach(row => {
                row.style.opacity = '0';
                row.style.transform = 'translateY(30px)';
                row.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            });

            observer.observe(table);
        });

        // Enhanced tooltip functionality
        document.querySelectorAll('.search-query').forEach(element => {
            if (element.scrollWidth > element.clientWidth) {
                element.style.cursor = 'help';
                element.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                    this.style.zIndex = '100';
                });
                element.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.zIndex = 'auto';
                });
            }
        });

        // Add subtle parallax effect to header
        const header = document.querySelector('.admin-dashboard-header');
        if (header) {
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const rate = scrolled * -0.5;
                header.style.transform = `translateY(${rate}px)`;
            });
        }

        // Enhanced icon animations
        document.querySelectorAll('.admin-stats-icon').forEach(icon => {
            icon.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.2) rotate(10deg)';
            });
            icon.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1) rotate(0deg)';
            });
        });
    });
</script>
{% endblock %}