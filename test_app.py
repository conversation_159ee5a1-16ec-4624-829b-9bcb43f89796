#!/usr/bin/env python
"""
Script de test pour vérifier le bon fonctionnement de l'application
"""
import os
import sys
import django

# Configuration de Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trend_clone.settings')
django.setup()

from django.contrib.auth.models import User
from core.models import UserProfile, Subscription, SubscriptionEvent
from django.utils import timezone
from datetime import timedelta

def create_test_admin():
    """Crée un administrateur de test"""
    username = 'admin_test'
    password = 'admin123'
    email = '<EMAIL>'

    if User.objects.filter(username=username).exists():
        print(f"L'utilisateur {username} existe déjà.")
        return

    user = User.objects.create_user(
        username=username,
        email=email,
        password=password,
        is_staff=True,
        is_superuser=True
    )

    profile, created = UserProfile.objects.get_or_create(user=user)

    print(f"Administrateur créé avec succès !")
    print(f"Nom d'utilisateur: {username}")
    print(f"Mot de passe: {password}")
    print(f"Email: {email}")

def create_test_premium_user():
    """Crée un utilisateur premium de test"""
    username = 'premium_test'
    password = 'premium123'
    email = '<EMAIL>'

    if User.objects.filter(username=username).exists():
        print(f"L'utilisateur {username} existe déjà. Mise à jour du profil...")
        user = User.objects.get(username=username)
        profile = user.profile
        profile.is_premium = True
        profile.subscription_active = True
        profile.subscription_start_date = timezone.now()
        profile.subscription_end_date = timezone.now() + timedelta(days=30)
        profile.stripe_customer_id = f'test_customer_{user.id}'
        profile.subscription_id = f'test_sub_{user.id}'
        profile.save()
        print(f"Profil mis à jour pour {username}")
        return

    user = User.objects.create_user(
        username=username,
        email=email,
        password=password
    )

    profile, created = UserProfile.objects.get_or_create(
        user=user,
        defaults={
            'is_premium': True,
            'subscription_active': True,
            'subscription_start_date': timezone.now(),
            'subscription_end_date': timezone.now() + timedelta(days=30),
            'stripe_customer_id': f'test_customer_{user.id}',
            'subscription_id': f'test_sub_{user.id}'
        }
    )

    subscription = Subscription.objects.create(
        user=user,
        stripe_subscription_id=f'test_sub_{user.id}',
        stripe_customer_id=f'test_customer_{user.id}',
        status='active',
        plan_id='test_plan',
        start_date=timezone.now(),
        end_date=timezone.now() + timedelta(days=30)
    )

    SubscriptionEvent.objects.create(
        subscription=subscription,
        event_type='created_for_testing',
        data={
            'message': 'Abonnement créé pour les tests',
            'created_by': 'test_script'
        }
    )

    print(f"Utilisateur premium créé avec succès !")
    print(f"Nom d'utilisateur: {username}")
    print(f"Mot de passe: {password}")
    print(f"Email: {email}")
    if profile.subscription_end_date:
        print(f"Statut: Premium (actif jusqu'au {profile.subscription_end_date.strftime('%d/%m/%Y')})")
    else:
        print("Statut: Premium (actif)")

def test_application():
    """Teste les fonctionnalités de base de l'application"""
    print("=== TEST DE L'APPLICATION ===")

    # Test des modèles
    print("\n1. Test des modèles...")
    total_users = User.objects.count()
    total_profiles = UserProfile.objects.count()
    premium_users = UserProfile.objects.filter(is_premium=True).count()

    print(f"   - Utilisateurs totaux: {total_users}")
    print(f"   - Profils totaux: {total_profiles}")
    print(f"   - Utilisateurs premium: {premium_users}")

    # Test de création d'utilisateurs
    print("\n2. Création d'utilisateurs de test...")
    create_test_admin()
    create_test_premium_user()

    print("\n=== TESTS TERMINÉS ===")
    print("\nVous pouvez maintenant :")
    print("1. Lancer le serveur : python manage.py runserver")
    print("2. Accéder à l'application : http://127.0.0.1:8000/")
    print("3. Tester l'interface administrateur : http://127.0.0.1:8000/admin-login/")
    print("4. Se connecter avec admin_test / admin123 (admin)")
    print("5. Se connecter avec premium_test / premium123 (utilisateur premium)")

if __name__ == '__main__':
    test_application()
