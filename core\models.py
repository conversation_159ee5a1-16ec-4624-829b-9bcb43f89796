from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone

class UserProfile(models.Model):
    """Profil utilisateur étendu avec informations d'abonnement"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    is_premium = models.BooleanField(default=False)
    search_count = models.IntegerField(default=0)
    last_search_reset = models.DateField(default=timezone.now)  # Pour réinitialiser quotidiennement
    subscription_id = models.CharField(max_length=100, blank=True, null=True)
    stripe_customer_id = models.CharField(max_length=100, blank=True, null=True)
    subscription_active = models.BooleanField(default=False)
    subscription_start_date = models.DateTimeField(blank=True, null=True)
    subscription_end_date = models.DateTimeField(blank=True, null=True)

    def __str__(self):
        return f"{self.user.username} - {'Premium' if self.is_premium else 'Gratuit'}"

    def can_search(self):
        """Vérifie si l'utilisateur peut effectuer une recherche"""
        if self.is_premium:
            return True

        # Pour l'instant, utilisation simple sans réinitialisation quotidienne
        # TODO: Implémenter la réinitialisation quotidienne après migration
        return self.search_count < 3

    def remaining_searches(self):
        """Retourne le nombre de recherches restantes pour un utilisateur gratuit"""
        if self.is_premium:
            return "Illimité"

        # Pour l'instant, utilisation simple sans réinitialisation quotidienne
        # TODO: Implémenter la réinitialisation quotidienne après migration
        return max(0, 3 - self.search_count)

    def is_subscription_active(self):
        """Vérifie si l'abonnement est toujours actif"""
        if not self.is_premium:
            return False
        if not self.subscription_end_date:
            return False
        return self.subscription_end_date > timezone.now()

# Les signaux sont définis dans signals.py

class Search(models.Model):
    """Modèle pour stocker les recherches effectuées par les utilisateurs"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='searches')
    query = models.CharField(max_length=255)
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username} - {self.query} - {self.timestamp.strftime('%d/%m/%Y %H:%M')}"

class SearchResult(models.Model):
    """Modèle pour stocker les résultats des recherches"""
    search = models.ForeignKey(Search, on_delete=models.CASCADE, related_name='results', null=True, blank=True)
    data_type = models.CharField(max_length=50, choices=[
        ('interest_over_time', 'Intérêt au fil du temps'),
        ('interest_by_region', 'Intérêt par région'),
        ('related_queries', 'Requêtes associées'),
        ('related_topics', 'Sujets associés')
    ], null=True, blank=True, default='interest_over_time')
    data = models.JSONField(default=dict)  # Stocke les données de tendance

    def __str__(self):
        if self.search:
            return f"{self.search.query} - {self.get_data_type_display()}"
        return f"{self.get_data_type_display()}"

class Subscription(models.Model):
    """Modèle pour suivre les abonnements"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='subscriptions')
    stripe_subscription_id = models.CharField(max_length=100)
    stripe_customer_id = models.CharField(max_length=100)
    status = models.CharField(max_length=50)
    plan_id = models.CharField(max_length=100)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - {self.status} - {self.end_date.strftime('%d/%m/%Y')}"

    def is_active(self):
        return self.status == 'active' and self.end_date > timezone.now()

class SubscriptionEvent(models.Model):
    """Modèle pour suivre les événements d'abonnement (création, annulation, etc.)"""
    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE, related_name='events')
    event_type = models.CharField(max_length=50)
    data = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.subscription.user.username} - {self.event_type} - {self.created_at.strftime('%d/%m/%Y %H:%M')}"
