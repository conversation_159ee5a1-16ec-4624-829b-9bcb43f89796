#!/usr/bin/env python
"""
Script de test pour la connexion utilisateur
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trend_clone.settings')
django.setup()

from django.contrib.auth.models import User
from django.contrib.auth import authenticate
from django.test import Client
from django.urls import reverse

def test_user_login():
    """Test de connexion utilisateur"""
    print("=== TEST DE CONNEXION UTILISATEUR ===\n")
    
    # Créer un client de test
    client = Client()
    
    # Tester avec des utilisateurs existants qui n'ont jamais eu de last_login
    users_to_test = User.objects.filter(last_login__isnull=True, is_active=True)[:3]
    
    print(f"Utilisateurs à tester (sans dernière connexion): {users_to_test.count()}")
    
    for user in users_to_test:
        print(f"\n--- Test avec l'utilisateur: {user.username} ---")
        print(f"Email: {user.email}")
        print(f"Actif: {user.is_active}")
        print(f"Date de création: {user.date_joined}")
        print(f"Dernière connexion: {user.last_login}")
        
        # Essayer de réinitialiser le mot de passe pour test
        test_password = "testpassword123"
        user.set_password(test_password)
        user.save()
        print(f"Mot de passe réinitialisé pour le test")
        
        # Test d'authentification directe
        auth_user = authenticate(username=user.username, password=test_password)
        if auth_user:
            print(f"✅ Authentification directe: SUCCÈS")
        else:
            print(f"❌ Authentification directe: ÉCHEC")
            continue
        
        # Test de connexion via l'interface web
        login_url = reverse('login')
        response = client.get(login_url)
        print(f"Page de connexion accessible: {response.status_code == 200}")
        
        # Tentative de connexion
        login_data = {
            'username': user.username,
            'password': test_password
        }
        
        response = client.post(login_url, login_data, follow=True)
        print(f"Réponse POST connexion: {response.status_code}")
        
        if response.status_code == 200:
            # Vérifier si l'utilisateur est connecté
            if hasattr(response, 'wsgi_request') and response.wsgi_request.user.is_authenticated:
                print(f"✅ Connexion web: SUCCÈS")
                print(f"Utilisateur connecté: {response.wsgi_request.user.username}")
                
                # Déconnecter pour le prochain test
                client.logout()
            else:
                print(f"❌ Connexion web: ÉCHEC - Utilisateur non connecté")
                # Afficher le contenu de la réponse pour debug
                if b"Erreur de connexion" in response.content:
                    print("Erreur de connexion détectée dans la réponse")
                if b"alert-danger" in response.content:
                    print("Message d'erreur présent dans la page")
        else:
            print(f"❌ Connexion web: ÉCHEC - Code de réponse: {response.status_code}")
    
    print("\n" + "="*50)
    
    # Test avec un utilisateur qui a déjà une last_login
    print("\n--- Test avec un utilisateur ayant déjà une connexion ---")
    existing_user = User.objects.filter(last_login__isnull=False, is_active=True).first()
    
    if existing_user:
        print(f"Utilisateur: {existing_user.username}")
        print(f"Dernière connexion: {existing_user.last_login}")
        
        # Réinitialiser le mot de passe
        test_password = "testpassword123"
        existing_user.set_password(test_password)
        existing_user.save()
        
        # Test d'authentification
        auth_user = authenticate(username=existing_user.username, password=test_password)
        if auth_user:
            print(f"✅ Authentification: SUCCÈS")
            
            # Test de connexion web
            login_data = {
                'username': existing_user.username,
                'password': test_password
            }
            
            response = client.post(reverse('login'), login_data, follow=True)
            if response.status_code == 200 and hasattr(response, 'wsgi_request') and response.wsgi_request.user.is_authenticated:
                print(f"✅ Connexion web: SUCCÈS")
            else:
                print(f"❌ Connexion web: ÉCHEC")
        else:
            print(f"❌ Authentification: ÉCHEC")
    
    print("\n" + "="*50)
    print("TEST TERMINÉ")

if __name__ == "__main__":
    test_user_login()
