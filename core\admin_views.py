from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth.models import User
from django.contrib.auth import authenticate, login, logout
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Count, Q
from django.utils import timezone
from django.conf import settings
from datetime import timedelta, datetime
import json
import calendar
from zoneinfo import ZoneInfo
from .models import UserProfile, Search
from .admin_forms import AdminRegistrationForm, AdminLoginForm

def is_admin(user):
    """Check if user is an admin"""
    if user.is_authenticated and user.is_staff:
        return True
    return False

def admin_register(request):
    """Admin registration view"""
    if request.method == 'POST':
        form = AdminRegistrationForm(request.POST)
        if form.is_valid():
            user = form.save()
            user.is_staff = True
            user.save()
            messages.success(request, "Compte administrateur créé avec succès ! Vous pouvez maintenant vous connecter.")
            return redirect('admin_login')
    else:
        form = AdminRegistrationForm()

    return render(request, 'admin/register.html', {'form': form})

def admin_login(request):
    """Admin login view"""
    # If user is already authenticated, log them out first
    if request.user.is_authenticated:
        logout(request)
        messages.info(request, "Vous avez été déconnecté de votre compte utilisateur.")

    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        if username and password:
            user = authenticate(request, username=username, password=password)

            if user is not None and user.is_staff:
                login(request, user)
                return redirect('admin_dashboard')
            else:
                messages.error(request, "Nom d'utilisateur ou mot de passe incorrect ou vous n'avez pas les droits d'administrateur.")
        else:
            messages.error(request, "Veuillez saisir un nom d'utilisateur et un mot de passe.")

        # Réutiliser les valeurs saisies pour le formulaire
        form = AdminLoginForm(initial={'username': username})
    else:
        form = AdminLoginForm()

    return render(request, 'admin/login.html', {'form': form})

@login_required(login_url='admin_login')
@user_passes_test(is_admin, login_url='admin_login')
def admin_dashboard(request):
    """Admin dashboard view"""
    # Get statistics
    total_users = User.objects.count()
    premium_users = UserProfile.objects.filter(is_premium=True).count()
    total_searches = Search.objects.count()

    # Get recent users
    recent_users = User.objects.order_by('-date_joined')[:10]

    # Get recent searches
    recent_searches = Search.objects.order_by('-timestamp')[:10]

    return render(request, 'admin/dashboard.html', {
        'active_menu': 'dashboard',
        'total_users': total_users,
        'premium_users': premium_users,
        'total_searches': total_searches,
        'recent_users': recent_users,
        'recent_searches': recent_searches,
        'stripe_dashboard_url': settings.STRIPE_DASHBOARD_URL
    })

@login_required(login_url='admin_login')
@user_passes_test(is_admin, login_url='admin_login')
def admin_subscribers(request):
    """Admin subscribers view"""
    # Get search parameters
    search_query = request.GET.get('search', '')
    status = request.GET.get('status', '')

    # Filter profiles with ordering
    profiles = UserProfile.objects.select_related('user').all().order_by('-user__date_joined')

    if search_query:
        profiles = profiles.filter(
            Q(user__username__icontains=search_query) |
            Q(user__email__icontains=search_query) |
            Q(user__first_name__icontains=search_query) |
            Q(user__last_name__icontains=search_query)
        )

    if status == 'premium':
        profiles = profiles.filter(is_premium=True)
    elif status == 'free':
        profiles = profiles.filter(is_premium=False)

    # Paginate results
    paginator = Paginator(profiles, 10)  # 10 profiles per page
    page = request.GET.get('page')
    profiles = paginator.get_page(page)

    return render(request, 'admin/subscribers.html', {
        'active_menu': 'subscribers',
        'profiles': profiles,
        'search_query': search_query,
        'status': status,
        'stripe_dashboard_url': settings.STRIPE_DASHBOARD_URL
    })

@login_required(login_url='admin_login')
@user_passes_test(is_admin, login_url='admin_login')
def admin_subscriptions(request):
    """Admin subscriptions view"""
    # Get search parameters
    search_query = request.GET.get('search', '')
    status = request.GET.get('status', '')

    # Filter profiles with ordering
    profiles = UserProfile.objects.select_related('user').all().order_by('-user__date_joined')

    if search_query:
        profiles = profiles.filter(
            Q(user__username__icontains=search_query) |
            Q(user__email__icontains=search_query) |
            Q(user__first_name__icontains=search_query) |
            Q(user__last_name__icontains=search_query)
        )

    if status == 'active':
        profiles = profiles.filter(is_premium=True)
    elif status == 'inactive':
        profiles = profiles.filter(is_premium=False)

    # Paginate results
    paginator = Paginator(profiles, 10)  # 10 profiles per page
    page = request.GET.get('page')
    profiles = paginator.get_page(page)

    return render(request, 'admin/subscriptions.html', {
        'active_menu': 'subscriptions',
        'profiles': profiles,
        'search_query': search_query,
        'status': status,
        'stripe_dashboard_url': 'https://dashboard.stripe.com/test/dashboard'
    })

@login_required(login_url='admin_login')
@user_passes_test(is_admin, login_url='admin_login')
def admin_subscriber_detail(request, user_id):
    """Admin subscriber detail view"""
    user = get_object_or_404(User, id=user_id)
    profile = get_object_or_404(UserProfile, user=user)
    searches = Search.objects.filter(user=user).order_by('-timestamp')

    # Traiter l'action de mise à jour du statut premium
    if request.method == 'POST' and 'action' in request.POST:
        action = request.POST.get('action')

        if action == 'make_premium':
            # Mettre à jour l'utilisateur en mode premium
            profile.is_premium = True
            profile.subscription_active = True
            profile.subscription_start_date = timezone.now()
            profile.subscription_end_date = timezone.now() + timedelta(days=30)
            profile.save()

            # Créer un enregistrement d'abonnement si le modèle existe
            from django.apps import apps
            if apps.is_installed('core') and apps.get_model('core', 'Subscription'):
                Subscription = apps.get_model('core', 'Subscription')
                SubscriptionEvent = apps.get_model('core', 'SubscriptionEvent')

                from django.conf import settings
                subscription = Subscription.objects.create(
                    user=user,
                    stripe_subscription_id=f"manual_{timezone.now().timestamp()}",
                    stripe_customer_id=f"manual_{user.id}",
                    status='active',
                    plan_id=getattr(settings, 'STRIPE_PRICE_ID', 'price_default'),
                    start_date=timezone.now(),
                    end_date=timezone.now() + timedelta(days=30)
                )

                # Enregistrer l'événement
                SubscriptionEvent.objects.create(
                    subscription=subscription,
                    event_type='created_manually',
                    data={"message": "Abonnement créé manuellement par l'administrateur"}
                )

            messages.success(request, f"L'utilisateur {user.username} est maintenant en mode Premium.")
            return redirect('admin_subscriber_detail', user_id=user_id)

        elif action == 'remove_premium':
            # Retirer le statut premium
            profile.is_premium = False
            profile.subscription_active = False
            profile.save()

            # Mettre à jour les abonnements actifs si le modèle existe
            from django.apps import apps
            if apps.is_installed('core') and apps.get_model('core', 'Subscription'):
                Subscription = apps.get_model('core', 'Subscription')
                SubscriptionEvent = apps.get_model('core', 'SubscriptionEvent')

                active_subscriptions = Subscription.objects.filter(
                    user=user,
                    status='active'
                )

                for subscription in active_subscriptions:
                    subscription.status = 'canceled'
                    subscription.save()

                    # Enregistrer l'événement
                    SubscriptionEvent.objects.create(
                        subscription=subscription,
                        event_type='canceled_manually',
                        data={"message": "Abonnement annulé manuellement par l'administrateur"}
                    )

            messages.success(request, f"Le statut Premium a été retiré de l'utilisateur {user.username}.")
            return redirect('admin_subscriber_detail', user_id=user_id)

    return render(request, 'admin/subscriber_detail.html', {
        'active_menu': 'subscribers',
        'user': user,
        'profile': profile,
        'searches': searches,
        'stripe_dashboard_url': 'https://dashboard.stripe.com/test/dashboard'
    })

@login_required(login_url='admin_login')
@user_passes_test(is_admin, login_url='admin_login')
def admin_change_status(request, user_id):
    """Change user subscription status"""
    if request.method == 'POST':
        user = get_object_or_404(User, id=user_id)
        profile = get_object_or_404(UserProfile, user=user)

        new_status = request.POST.get('new_status')

        if new_status == 'premium':
            profile.is_premium = True
            messages.success(request, f"Le statut de {user.username} a été changé en Premium.")
        elif new_status == 'free':
            profile.is_premium = False
            messages.success(request, f"Le statut de {user.username} a été changé en Gratuit.")

        profile.save()

        # Redirect back to the referring page
        referer = request.META.get('HTTP_REFERER')
        if 'subscriber_detail' in referer:
            return redirect('admin_subscriber_detail', user_id=user_id)
        elif 'subscriptions' in referer:
            return redirect('admin_subscriptions')
        else:
            return redirect('admin_subscribers')

    return redirect('admin_subscribers')

@login_required(login_url='admin_login')
@user_passes_test(is_admin, login_url='admin_login')
def admin_stats(request):
    """Admin statistics view"""
    # Get user statistics
    total_users = User.objects.count()
    premium_users = UserProfile.objects.filter(is_premium=True).count()
    free_users = total_users - premium_users

    # Get search statistics
    total_searches = Search.objects.count()

    # Get top searches
    top_searches_query = Search.objects.values('query').annotate(count=Count('query')).order_by('-count')[:10]

    top_searches = []
    for item in top_searches_query:
        percentage = (item['count'] / total_searches) * 100 if total_searches > 0 else 0
        top_searches.append({
            'query': item['query'],
            'count': item['count'],
            'percentage': percentage
        })

    # Get searches per month for the last 6 months
    months = []
    searches_per_month = []

    for i in range(5, -1, -1):
        date = timezone.now() - timedelta(days=30 * i)
        month_name = calendar.month_name[date.month]
        months.append(f"{month_name} {date.year}")

        # Utiliser ZoneInfo au lieu de timezone.utc
        utc = ZoneInfo('UTC')
        start_date = datetime(date.year, date.month, 1, tzinfo=utc)
        if date.month == 12:
            end_date = datetime(date.year + 1, 1, 1, tzinfo=utc)
        else:
            end_date = datetime(date.year, date.month + 1, 1, tzinfo=utc)

        count = Search.objects.filter(timestamp__gte=start_date, timestamp__lt=end_date).count()
        searches_per_month.append(count)

    return render(request, 'admin/stats.html', {
        'active_menu': 'stats',
        'total_users': total_users,
        'premium_users': premium_users,
        'free_users': free_users,
        'total_searches': total_searches,
        'top_searches': top_searches,
        'months': json.dumps(months),
        'searches_per_month': searches_per_month,
        'stripe_dashboard_url': 'https://dashboard.stripe.com/test/dashboard'
    })
