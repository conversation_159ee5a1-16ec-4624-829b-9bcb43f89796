{% extends 'base.html' %}

{% block title %}Connexion - Clone Google Trends{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h2 class="text-center">Connexion</h2>
            </div>
            <div class="card-body">
                <!-- Affichage des messages d'erreur -->
                {% if form.errors %}
                    <div class="alert alert-danger" role="alert">
                        <h6 class="alert-heading">Erreur de connexion :</h6>
                        {% for field, errors in form.errors.items %}
                            {% for error in errors %}
                                <p class="mb-1">{{ error }}</p>
                            {% endfor %}
                        {% endfor %}
                        {% if form.non_field_errors %}
                            {% for error in form.non_field_errors %}
                                <p class="mb-1">{{ error }}</p>
                            {% endfor %}
                        {% endif %}
                    </div>
                {% endif %}

                <!-- Affichage des messages Django -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="post">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">{{ form.username.label }}</label>
                        {{ form.username }}
                        {% if form.username.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.username.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label for="{{ form.password.id_for_label }}" class="form-label">{{ form.password.label }}</label>
                        {{ form.password }}
                        {% if form.password.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.password.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Se connecter</button>
                    </div>
                </form>
                <div class="text-center mt-3">
                    <p>Vous n'avez pas de compte ? <a href="{% url 'register' %}">S'inscrire</a></p>
                    <p><a href="{% url 'password_reset' %}">Mot de passe oublié ?</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
