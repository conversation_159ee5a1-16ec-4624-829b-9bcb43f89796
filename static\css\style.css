
/* Global Font Family - Times New Roman */
* {
    font-family: "Times New Roman", Times, serif !important;
}

body {
    font-family: "Times New Roman", Times, serif;
    background-color: #f7f7f7;
}

/* Ensure Times New Roman applies to all elements */
h1, h2, h3, h4, h5, h6,
p, span, div, a, button,
input, textarea, select,
.btn, .form-control, .form-select,
.navbar-brand, .nav-link,
.card-title, .card-text,
.table, .badge, .alert {
    font-family: "Times New Roman", Times, serif !important;
}

.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

/* Admin Status Indicator */
.admin-status-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    padding: 0.75rem 1.25rem;
    border-radius: 25px;
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
    font-weight: 700;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    animation: adminPulse 2s infinite;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.admin-status-indicator:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 35px rgba(220, 53, 69, 0.4);
    background: linear-gradient(135deg, #e74c3c, #dc3545);
}

.admin-status-indicator i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
    animation: adminIconSpin 3s linear infinite;
}

@keyframes adminPulse {
    0%, 100% {
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
    }
    50% {
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.6);
    }
}

@keyframes adminIconSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Admin Status Indicator for Mobile */
@media (max-width: 768px) {
    .admin-status-indicator {
        top: 10px;
        right: 10px;
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
        border-radius: 20px;
    }

    .admin-status-indicator i {
        font-size: 1rem;
        margin-right: 0.3rem;
    }
}

/* Admin Navigation Enhancement */
.navbar .admin-mode-badge {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: 1rem;
    animation: adminBadgePulse 1.5s infinite;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

@keyframes adminBadgePulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

/* Admin Footer Indicator */
.admin-footer-indicator {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: rgba(220, 53, 69, 0.9);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 9998;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.admin-footer-indicator:hover {
    background: rgba(220, 53, 69, 1);
    transform: translateY(-2px);
}

/* Blue Theme Enhancement */
:root {
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --accent-color: #7209b7;
    --success-color: #22c55e;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --dark-color: #1e293b;
    --gray-color: #64748b;
    --light-gray: #f1f5f9;
    --border-color: #e2e8f0;
}

/* Enhanced Blue Buttons */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    font-family: "Times New Roman", Times, serif !important;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(67, 97, 238, 0.3);
}

/* Enhanced Blue Links */
a {
    color: var(--primary-color);
    font-family: "Times New Roman", Times, serif !important;
    transition: all 0.3s ease;
}

a:hover {
    color: var(--secondary-color);
    text-decoration: none;
}

/* Blue Theme for Forms */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

/* Blue Theme for Navigation */
.navbar-brand {
    color: var(--primary-color) !important;
    font-weight: 700;
    font-family: "Times New Roman", Times, serif !important;
}

.nav-link {
    font-family: "Times New Roman", Times, serif !important;
    font-weight: 500;
}

.nav-link:hover {
    color: var(--primary-color) !important;
}
