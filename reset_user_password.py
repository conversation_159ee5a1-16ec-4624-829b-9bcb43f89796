#!/usr/bin/env python
"""
Script pour réinitialiser le mot de passe d'un utilisateur
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trend_clone.settings')
django.setup()

from django.contrib.auth.models import User

def reset_password():
    """Réinitialise le mot de passe d'un utilisateur pour test"""
    
    # Choisir un utilisateur sans dernière connexion
    user = User.objects.filter(username='soso11').first()
    
    if user:
        print(f"Utilisateur trouvé: {user.username}")
        print(f"Email: {user.email}")
        print(f"Actif: {user.is_active}")
        print(f"Dernière connexion: {user.last_login}")
        
        # Réinitialiser le mot de passe
        new_password = "test123"
        user.set_password(new_password)
        user.save()
        
        print(f"\nMot de passe réinitialisé pour {user.username}")
        print(f"Nouveau mot de passe: {new_password}")
        print(f"\nVous pouvez maintenant tester la connexion avec:")
        print(f"Nom d'utilisateur: {user.username}")
        print(f"Mot de passe: {new_password}")
        
    else:
        print("Utilisateur 'soso11' non trouvé")
        
        # Afficher les utilisateurs disponibles
        users = User.objects.filter(is_active=True)[:5]
        print("\nUtilisateurs disponibles:")
        for u in users:
            print(f"- {u.username} ({u.email})")

if __name__ == "__main__":
    reset_password()
