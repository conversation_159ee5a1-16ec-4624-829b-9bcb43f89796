#!/usr/bin/env python
"""
Script de diagnostic pour les problèmes d'authentification
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trend_clone.settings')
django.setup()

from django.contrib.auth.models import User
from django.contrib.auth import authenticate
from core.models import UserProfile

def diagnose_authentication():
    """Diagnostique les problèmes d'authentification"""
    print("=== DIAGNOSTIC D'AUTHENTIFICATION ===\n")
    
    # 1. Vérifier tous les utilisateurs
    print("1. UTILISATEURS DANS LA BASE DE DONNÉES:")
    users = User.objects.all()
    print(f"   Nombre total d'utilisateurs: {users.count()}")
    
    for user in users:
        print(f"\n   Utilisateur: {user.username}")
        print(f"   - Email: {user.email}")
        print(f"   - Actif: {user.is_active}")
        print(f"   - Staff: {user.is_staff}")
        print(f"   - Superuser: {user.is_superuser}")
        print(f"   - Date de création: {user.date_joined}")
        print(f"   - Dernière connexion: {user.last_login}")
        
        # Vérifier le profil
        try:
            profile = user.profile
            print(f"   - Profil existe: Oui")
            print(f"   - Premium: {profile.is_premium}")
            print(f"   - Recherches: {profile.search_count}")
        except UserProfile.DoesNotExist:
            print(f"   - Profil existe: NON - PROBLÈME DÉTECTÉ!")
    
    print("\n" + "="*50)
    
    # 2. Tester l'authentification avec un utilisateur existant
    print("\n2. TEST D'AUTHENTIFICATION:")
    
    if users.exists():
        test_user = users.first()
        print(f"   Test avec l'utilisateur: {test_user.username}")
        
        # Essayer d'authentifier avec un mot de passe vide (ne devrait pas marcher)
        auth_result = authenticate(username=test_user.username, password="")
        print(f"   - Auth avec mot de passe vide: {auth_result}")
        
        # Essayer d'authentifier avec un mot de passe incorrect
        auth_result = authenticate(username=test_user.username, password="wrongpassword")
        print(f"   - Auth avec mot de passe incorrect: {auth_result}")
        
        print(f"   - Hash du mot de passe: {test_user.password[:50]}...")
    
    print("\n" + "="*50)
    
    # 3. Vérifier les profils orphelins
    print("\n3. VÉRIFICATION DES PROFILS:")
    profiles = UserProfile.objects.all()
    print(f"   Nombre total de profils: {profiles.count()}")
    
    for profile in profiles:
        try:
            user = profile.user
            print(f"   - Profil pour {user.username}: OK")
        except User.DoesNotExist:
            print(f"   - Profil orphelin détecté: ID {profile.id}")
    
    # Vérifier les utilisateurs sans profil
    users_without_profile = []
    for user in users:
        try:
            profile = user.profile
        except UserProfile.DoesNotExist:
            users_without_profile.append(user)
    
    if users_without_profile:
        print(f"\n   UTILISATEURS SANS PROFIL DÉTECTÉS:")
        for user in users_without_profile:
            print(f"   - {user.username} (ID: {user.id})")
    
    print("\n" + "="*50)
    
    # 4. Créer un utilisateur de test
    print("\n4. CRÉATION D'UTILISATEUR DE TEST:")
    test_username = "test_debug_user"
    test_email = "<EMAIL>"
    test_password = "testpassword123"
    
    # Supprimer l'utilisateur de test s'il existe
    User.objects.filter(username=test_username).delete()
    
    try:
        # Créer l'utilisateur
        test_user = User.objects.create_user(
            username=test_username,
            email=test_email,
            password=test_password
        )
        print(f"   Utilisateur de test créé: {test_user.username}")
        
        # Vérifier le profil
        try:
            profile = test_user.profile
            print(f"   - Profil automatiquement créé: Oui")
        except UserProfile.DoesNotExist:
            print(f"   - Profil automatiquement créé: NON - PROBLÈME!")
            # Créer manuellement le profil
            profile = UserProfile.objects.create(user=test_user)
            print(f"   - Profil créé manuellement: Oui")
        
        # Tester l'authentification
        auth_user = authenticate(username=test_username, password=test_password)
        if auth_user:
            print(f"   - Test d'authentification: SUCCÈS")
        else:
            print(f"   - Test d'authentification: ÉCHEC")
        
        # Nettoyer
        test_user.delete()
        print(f"   - Utilisateur de test supprimé")
        
    except Exception as e:
        print(f"   ERREUR lors de la création: {e}")
    
    print("\n" + "="*50)
    print("\nDIAGNOSTIC TERMINÉ")

if __name__ == "__main__":
    diagnose_authentication()
