{% extends 'base.html' %}

{% block title %}Accueil - Ana<PERSON><PERSON> de <PERSON>s{% endblock %}

{% block extra_css %}
<style>
    :root {
        --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        --shadow-soft: 0 10px 30px rgba(0, 0, 0, 0.08);
        --shadow-medium: 0 20px 40px rgba(0, 0, 0, 0.12);
        --shadow-strong: 0 30px 60px rgba(0, 0, 0, 0.15);
    }

    .hero-section {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border-radius: 25px;
        padding: 5rem 2rem;
        margin-bottom: 5rem;
        position: relative;
        overflow: hidden;
        box-shadow: var(--shadow-medium);
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background-image: url('https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
        background-size: cover;
        background-position: center;
        opacity: 0.15;
        z-index: 0;
    }

    .hero-section::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
        animation: shimmer 3s infinite;
        z-index: 1;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .hero-content {
        position: relative;
        z-index: 2;
    }

    .hero-content h1 {
        animation: fadeInUp 1s ease-out;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .hero-content p {
        animation: fadeInUp 1s ease-out 0.2s both;
    }

    .hero-content .btn {
        animation: fadeInUp 1s ease-out 0.4s both;
        transition: var(--transition-smooth);
    }

    .hero-content .btn:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 15px 30px rgba(0,0,0,0.2);
    }

    .feature-icon {
        font-size: 3rem;
        margin-bottom: 1.5rem;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        transition: var(--transition-smooth);
    }

    .choice-card {
        border-radius: 20px;
        overflow: hidden;
        transition: var(--transition-smooth);
        border: none;
        box-shadow: var(--shadow-soft);
        position: relative;
    }

    .choice-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        transform: scaleX(0);
        transition: var(--transition-smooth);
    }

    .choice-card:hover::before {
        transform: scaleX(1);
    }

    .choice-card:hover {
        transform: translateY(-15px) scale(1.02);
        box-shadow: var(--shadow-strong);
    }

    .choice-card .card-header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 2rem 1.5rem;
        text-align: center;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .choice-card .card-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: var(--transition-smooth);
    }

    .choice-card:hover .card-header::before {
        left: 100%;
    }

    .choice-card .card-header i {
        transition: var(--transition-smooth);
    }

    .choice-card:hover .card-header i {
        transform: scale(1.1) rotate(5deg);
    }

    .choice-card .card-body {
        padding: 2.5rem;
    }

    .card {
        transition: var(--transition-smooth);
        border: none;
        box-shadow: var(--shadow-soft);
        border-radius: 16px;
    }

    .card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-medium);
    }

    .rounded-circle {
        transition: var(--transition-smooth);
    }

    .card:hover .rounded-circle {
        transform: scale(1.1);
    }

    .card:hover .feature-icon {
        transform: scale(1.2);
    }

    .btn {
        transition: var(--transition-smooth);
        position: relative;
        overflow: hidden;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: var(--transition-smooth);
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.15);
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
        animation: pulse 2s infinite;
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
        .hero-section {
            padding: 3rem 1.5rem;
            margin-bottom: 3rem;
        }

        .choice-card .card-body {
            padding: 2rem;
        }

        .feature-icon {
            font-size: 2.5rem;
        }
    }

    /* Loading animation for better UX */
    .loading-shimmer {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: shimmer 1.5s infinite;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section" data-aos="fade-up">
    <div class="hero-content text-center">
        <h1 class="display-4 fw-bold mb-4">Découvrez les tendances qui façonnent le web</h1>
        <p class="lead mb-5">Explorez les tendances de recherche en temps réel et obtenez des insights précieux sur ce que le monde recherche.</p>
        <a href="{% url 'user_choice' %}" class="btn btn-light btn-lg px-5 py-3 me-3">
            <i class="bi bi-search me-2"></i>Commencer maintenant
        </a>
        <a href="#fonctionnalites" class="btn btn-outline-light btn-lg px-5 py-3">
            <i class="bi bi-info-circle me-2"></i>En savoir plus
        </a>
    </div>
</section>

<!-- User Choice Section -->
<section class="mb-5" data-aos="fade-up">
    <div class="row justify-content-center">
        <div class="col-md-10 text-center mb-5">
            <h2 class="fw-bold mb-4">Choisissez votre mode d'accès</h2>
            <p class="text-muted mb-5">Notre plateforme offre deux modes d'accès distincts pour répondre à vos besoins spécifiques.</p>
        </div>

        <div class="col-lg-5 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
            <div class="choice-card card h-100">
                <div class="card-header">
                    <i class="bi bi-person-fill display-4 mb-3"></i>
                    <h3 class="card-title">Utilisateur</h3>
                </div>
                <div class="card-body d-flex flex-column">
                    <p class="card-text flex-grow-1">Accédez à notre plateforme pour explorer les tendances de recherche, créer des visualisations personnalisées et suivre l'évolution des sujets qui vous intéressent.</p>
                    <ul class="list-unstyled mb-4">
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>Recherches illimitées (premium)</li>
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>Visualisations avancées</li>
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>Historique des recherches</li>
                        <li><i class="bi bi-check-circle-fill text-success me-2"></i>Exportation des données</li>
                    </ul>
                    <a href="{% url 'user_choice' %}" class="btn btn-primary btn-lg w-100">
                        <i class="bi bi-arrow-right-circle me-2"></i>Continuer en tant qu'utilisateur
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-5 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="200">
            <div class="choice-card card h-100">
                <div class="card-header">
                    <i class="bi bi-shield-lock-fill display-4 mb-3"></i>
                    <h3 class="card-title">Administrateur</h3>
                </div>
                <div class="card-body d-flex flex-column">
                    <p class="card-text flex-grow-1">Gérez les abonnements, suivez les statistiques d'utilisation et administrez tous les aspects de la plateforme depuis une interface dédiée.</p>
                    <ul class="list-unstyled mb-4">
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>Gestion des utilisateurs</li>
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>Suivi des abonnements</li>
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>Statistiques d'utilisation</li>
                        <li><i class="bi bi-check-circle-fill text-success me-2"></i>Configuration système</li>
                    </ul>
                    <a href="{% url 'admin_login' %}" class="btn btn-secondary btn-lg w-100">
                        <i class="bi bi-arrow-right-circle me-2"></i>Continuer en tant qu'administrateur
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- How it Works Section -->
<section id="fonctionnalites" class="py-5 mb-5 bg-light rounded-4 p-4" data-aos="fade-up">
    <div class="text-center mb-5">
        <h2 class="fw-bold mb-4">Comment ça marche ?</h2>
        <p class="text-muted mb-5 col-md-8 mx-auto">Trois étapes simples pour commencer à analyser les tendances</p>
    </div>

    <div class="row">
        <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="100">
            <div class="card border-0 h-100">
                <div class="card-body text-center p-4">
                    <div class="rounded-circle bg-primary bg-opacity-10 d-inline-flex p-4 mb-4">
                        <i class="bi bi-search display-4 text-primary"></i>
                    </div>
                    <h3 class="h4 mb-3">1. Rechercher</h3>
                    <p class="text-muted">Saisissez vos mots-clés pour découvrir les tendances actuelles et historiques.</p>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="200">
            <div class="card border-0 h-100">
                <div class="card-body text-center p-4">
                    <div class="rounded-circle bg-success bg-opacity-10 d-inline-flex p-4 mb-4">
                        <i class="bi bi-graph-up-arrow display-4 text-success"></i>
                    </div>
                    <h3 class="h4 mb-3">2. Analyser</h3>
                    <p class="text-muted">Notre système récupère les données en temps réel et les traite pour vous offrir des insights pertinents.</p>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="300">
            <div class="card border-0 h-100">
                <div class="card-body text-center p-4">
                    <div class="rounded-circle bg-warning bg-opacity-10 d-inline-flex p-4 mb-4">
                        <i class="bi bi-bar-chart-fill display-4 text-warning"></i>
                    </div>
                    <h3 class="h4 mb-3">3. Visualiser</h3>
                    <p class="text-muted">Découvrez les résultats sous forme de graphiques interactifs et de visualisations claires.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-5 mb-5" data-aos="fade-up">
    <div class="text-center mb-5">
        <h2 class="fw-bold mb-4">Fonctionnalités principales</h2>
        <p class="text-muted mb-5 col-md-8 mx-auto">Découvrez les outils puissants qui vous aideront à comprendre et analyser les tendances de recherche.</p>
    </div>

    <div class="row">
        <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
            <div class="card h-100">
                <div class="card-body text-center p-4">
                    <i class="bi bi-graph-up-arrow feature-icon"></i>
                    <h3 class="h4 mb-3">Analyse des tendances</h3>
                    <p class="text-muted">Découvrez ce que les gens recherchent en temps réel avec des données précises et actualisées.</p>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="200">
            <div class="card h-100">
                <div class="card-body text-center p-4">
                    <i class="bi bi-pie-chart feature-icon"></i>
                    <h3 class="h4 mb-3">Visualisations avancées</h3>
                    <p class="text-muted">Transformez les données en graphiques interactifs et visuellement attrayants pour une meilleure compréhension.</p>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="300">
            <div class="card h-100">
                <div class="card-body text-center p-4">
                    <i class="bi bi-clock-history feature-icon"></i>
                    <h3 class="h4 mb-3">Historique complet</h3>
                    <p class="text-muted">Accédez à l'historique de vos recherches et suivez l'évolution des tendances dans le temps.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="text-center py-5 mb-5" data-aos="fade-up">
    <h2 class="fw-bold mb-4">Prêt à explorer les tendances ?</h2>
    <p class="text-muted mb-5 col-md-8 mx-auto">Rejoignez des milliers d'utilisateurs qui utilisent notre plateforme pour découvrir et analyser les tendances de recherche.</p>
    <a href="{% url 'user_choice' %}" class="btn btn-primary btn-lg px-5 py-3">
        <i class="bi bi-rocket-takeoff me-2"></i>Commencer gratuitement
    </a>
</section>
{% endblock %}
